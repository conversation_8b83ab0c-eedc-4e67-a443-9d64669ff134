<?php

namespace App\Http\Controllers\api;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\Bulk;
use App\Models\Hiso;
use App\Models\Hiyk;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class UploadController extends Controller
{
    private $has_error = false;
    private $errors = [];
    private $logs = [];

    public function uload(Request $request)
    {
        $codeLog1 = 'EM2001';
        $codeLog2 = 'EM2002';
        $codeLog3 = 'EM2003';
        $this->logs = ['url' => $request->url(), 'request' => $request->all(),];
        $isErrorRequest = $this->isErrorRequest($request, 'uload');
        if($isErrorRequest) {
            AppHelper::writeLog($this->logs, $codeLog1);
            return $isErrorRequest;
        }

        $this->common($request);
        if($this->has_error && count($this->errors)){
            $this->logs['errors'] = $this->errors;
            AppHelper::writeLog($this->logs, $codeLog2);
            return response(['code' => 400, 'message' => 'Success', 'errors' => $this->errors],200);
        }
        else if($this->has_error) {
            AppHelper::writeLog($this->logs, $codeLog3);
            return AppHelper::resError(400,AppHelper::$DEFAULT_400_MESS);
        }
        return response(['code' => 200, 'message' => 'Success',],200);
    }

    public function uloadblk(Request $request)
    {
        $codeLog1 = 'EM2004';
        $codeLog2 = 'EM2005';
        $codeLog3 = 'EM2006';

        $this->logs = ['url' => $request->url(), 'request' => $request->all(),];
        $isErrorRequest = $this->isErrorRequest($request, 'uloadblk');
        if($isErrorRequest) {
            AppHelper::writeLog($this->logs, $codeLog1);
            return $isErrorRequest;
        }

        $this->common($request);
        if($this->has_error){
            $codeLog = $codeLog2;
            $this->logs['errors'] = $this->errors;
            if(!count($this->errors)) $codeLog = $codeLog3;
            AppHelper::writeLog($this->logs, $codeLog);
            return response(['code' => 400, 'message' => 'Success', 'errors' => $this->errors],200);
        }
        return response(['code' => 200, 'message' => 'Success',],200);
    }

    private function isErrorRequest($request, $api)
    {
        $validator = Validator::make($request->all(), [
            'upload' => 'bail|required|array',
        ]);
        if ($validator->fails()) {
            $this->logs['message'] = $validator->messages()->all();
            // $message = implode(', ', $validator->messages()->all());
            return AppHelper::resError(400,AppHelper::$DEFAULT_400_MESS);
        }
        foreach ($request->upload as $upload){
            if(
                !isset($upload['fileType'])
                || !in_array($upload['fileType'],AppHelper::$uploadApi[$api])
                || !isset($upload['datas'])
                || !is_array($upload['datas'])
            ) {
                $this->logs['message'] = 'request invalid.';
                return AppHelper::resError(400,AppHelper::$DEFAULT_400_MESS);
            }
        }
        return false;
    }

    private function common($request)
    {
        $timeNow = strtotime('now');
        $randomStr = $timeNow.'-'.Str::random(90);
        $dateTime = date('Y-m-d H:i:s');
        $user = auth('api')->user();
        try {
            DB::beginTransaction();
            $pdo = DB::connection()->getPdo();
            foreach ($request->upload as $upload){
                $this->errors[$upload['fileType']] = [];
                $nameTb = $this->createTempTbIfNotHave($upload['fileType'], $pdo);
                $tb = $nameTb[0];
                $tbTemp = $nameTb[1];
                $rules = AppHelper::$rules[$upload['fileType']];
                $fullField = [];
                foreach ($rules as $field => $fieldRule){
                    $fullField[$field] = null;
                }
                $numField = count($fullField);
                $sql = '';
                $strField = implode(',',array_keys($rules)).',created_at,updated_at';
                foreach ($upload['datas'] as $k => $eUploadData){
                    $uploadData = explode(',',$randomStr.','.$eUploadData);
                    $uploadData = $this->addHaisoCenterCd($uploadData,2, $user);
                    if(in_array($upload['fileType'], ['30_HISO'])) {
                        $uploadData = $this->changeDate($uploadData,8);//HaisoDt
                        $uploadData = $this->addHaisoTenkens($uploadData,29);
                    }
                    else if(in_array($upload['fileType'], ['31_HIYK'])) {
                        $uploadData = $this->changeDate($uploadData,9);//HaisoDt

                        if(isset($uploadData[15]) && strlen($uploadData[15]) == 8){//SoshinDt
                            $uploadData[15] = substr($dateTime,0,10).' '.$uploadData[15];
                        }
                    }
                    else if(in_array($upload['fileType'], ['33_BULK'])) {
                        $uploadData = $this->changeDate($uploadData,8);//HaisoDt
                        $uploadData = $this->addHaisoTenkens($uploadData,23);
                    }
                    if(count($uploadData) != $numField){
                        $this->has_error = true;
                        $this->errors[$upload['fileType']][$k] = 'row '.($k+1).' : columns are not match.';
                        continue;
                    }

                    $tempData = $fullField;
                    $i = 0;
                    foreach ($fullField as $key => $value){
                        $v = $uploadData[$i];
                        if(in_array($key,AppHelper::$dateField[$upload['fileType']]))
                            $v = AppHelper::checkDateTime($uploadData[$i]);
                        $tempData[$key] = $v;
                        $i++;
                    }
                    $tempData['created_at'] = $dateTime;
                    $tempData['updated_at'] = $dateTime;

                    $validator = Validator::make($tempData, $rules);
                    if ($validator->fails()){
                        $message = implode(', ', $validator->messages()->all());
                        $this->has_error = true;
                        $this->errors[$upload['fileType']][$k] = 'row '.($k+1).' : '.$message;
                        // dd($message);
                        continue;
                    }
                    $sql .= "INSERT INTO ".$tbTemp." (".$strField.") VALUES (";
                    $r = array_map(function ($aL){return ($aL === null ? 'NULL' : AppHelper::replaceStringSql($aL));},$tempData);
                    $sql .= implode(",", $r);
                    $sql .= ");";
                    if(!$k%AppHelper::$LIMIT_INS && $sql){
                        // DB::statement($sql);
                        $pdo->exec($sql);
                        $sql = '';
                    }
                }
                // if($sql) DB::statement($sql);
                if($sql) $pdo->exec($sql);
                $priField = AppHelper::$uploadFileType[$upload['fileType']]['primary'];
                $updField = AppHelper::$uploadFileType[$upload['fileType']]['update'];
                $this->lastSql($tb,$tbTemp,$priField,$updField,$randomStr,$pdo);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->logs['exception'] = $e->getMessage() . ' - ' . $e->getLine();
            // dd($th->getMessage());
            $this->has_error = true;
            $this->errors = [];
        }
    }

    private function addHaisoCenterCd($uploadData,$key, $user)
    {
        if(isset($uploadData[$key])){
            $uploadData = array_merge(array_slice($uploadData,0,$key),[$user->HaisoCenterCd],array_slice($uploadData,$key));
        }
        return $uploadData;
    }

    private function addHaisoTenkens($uploadData,$key)
    {
        if(isset($uploadData[$key]) && strlen($uploadData[$key]) == 26){
            $uploadData = array_merge(array_slice($uploadData,0,$key),str_split($uploadData[$key]),array_slice($uploadData,$key+1));
        }
        return $uploadData;
    }

    private function changeDate($uploadData,$key)
    {
        if(isset($uploadData[$key]) && strlen($uploadData[$key]) == 8){
            $t = str_split($uploadData[$key],2);
            $uploadData[$key] = $t[0].$t[1].'-'.$t[2].'-'.$t[3];
        }
        return $uploadData;
    }

    private function lastSql($tb,$tbTemp,$priField,$updField,$randomStr, $pdo)
    {
        $fullField = array_merge($priField,$updField,['created_at','updated_at']);
        if(in_array($tb,['t_hiyk','t_bulk'])){
            $fullField = array_merge(AppHelper::$insertTb[$tb],['created_at','updated_at']);
        }
        $fullCols = implode(',',$fullField);
        $priCols = implode(',',$priField);
        $strFullField = implode( ',', array_map( function( $value ) {
                return 't2.'.$value;
            } , $fullField )
        );
        $wheres = implode( ' AND ', array_map( function( $value ) {
                return 't1.'.$value.' = t2.'.$value;
            } , $priField )
        );
        $whereNull = implode( ' AND ', array_map( function( $value ) {
                return 't1.'.$value.' IS NULL';
            } , $priField )
        );
        $updateColumns = array_merge($updField,['updated_at']);
        $set = implode( ',', array_map( function( $value ) {
                return 't1.'.$value.' = t2.'.$value;
            } , $updateColumns )
        );
        $sqlDelete = "";
        if(in_array($tb,['t_hiyk'])){
            $arr = [
                'KigyoCd', 'HaisoCenterCd', 'BusyoCd', 'EigyoCd',
                'HanbaiCd', 'BukkenNo', 'HaisoTantCd'
            ];
            $onDelete = implode( ' AND ', array_map( function( $value ) {
                    return 'q.'.$value.' = u.'.$value;
                } , $arr )
            );
            $sqlDelete = "
            DELETE FROM {$tb}
            WHERE id IN 
                ( SELECT q.id
                    FROM {$tb} q
                    INNER JOIN {$tbTemp} u ON {$onDelete}
                    GROUP BY q.id
                );
            ";
        }

        $sql = "
            DELETE FROM {$tbTemp} WHERE id NOT IN (
                SELECT id FROM (
                    SELECT MAX(id) as id,{$priCols} 
                    FROM {$tbTemp}
                    GROUP BY {$priCols}
                ) 
                AS t1
            );

            INSERT INTO {$tb} SELECT {$strFullField} FROM {$tbTemp} t2
            LEFT JOIN {$tb} t1 ON {$wheres}
            WHERE {$whereNull};

            UPDATE t1 
                SET {$set} 
                FROM {$tb} t1, {$tbTemp} t2 
                WHERE 
                    {$wheres};
        ";

        $pdo->exec($sqlDelete.$sql);
    }

    private function createTempTbIfNotHave($fileType, $pdo)
    {
        $tb = '';
        $tbTemp = '';
        switch ($fileType){
            case '30_HISO':
                $tb = 't_hiso';
                $tbTemp = $this->createHiso($pdo);
                break;
            case '31_HIYK':
                $tb = 't_hiyk';
                $tbTemp = $this->createHiyk($pdo);
                break;
            case '33_BULK':
                $tb = 't_bulk';
                $tbTemp = $this->createBulk($pdo);
                break;
        }
        return [$tb,$tbTemp];
    }

    private function createHiso($pdo)
    {
        $tbTemp = '#t_hiso_temp';
        $sql = $this->sqlCreateHiso($tbTemp);
        $sql .= $this->sqlCreateHiso($tbTemp.'_1');
        // DB::statement($sql);
        $pdo->exec($sql);
        return $tbTemp;
    }

    private function sqlCreateHiso($tbName){
        $sql = "
            CREATE TABLE {$tbName}(
                [id] [bigint] IDENTITY(1,1) NOT NULL,
                [RandomString] [varchar](200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
                [KigyoCd] [int] NOT NULL,
                [HaisoCenterCd] [decimal](4, 0) NOT NULL,
                [BusyoCd] [int] NOT NULL,
                [EigyoCd] [int] NOT NULL,
                [HanbaiCd] [decimal](4, 0) NOT NULL,
                [BukkenNo] [varchar](12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
                [RirekiNo] [decimal](3, 0) NOT NULL,
                [HaisoDt] [date] NOT NULL,
                [IdouKbn] [decimal](4, 0) NOT NULL,
                [HaisoTantCd] [decimal](8, 0) NULL,
                [SechiKeitai] [decimal](4, 0) NULL,
                [Yoki11Ryo] [decimal](5, 0) NULL,
                [Yoki11Num] [decimal](3, 0) NULL,
                [Yoki21Ryo] [decimal](5, 0) NULL,
                [Yoki21Num] [decimal](3, 0) NULL,
                [Yoki12Ryo] [decimal](5, 0) NULL,
                [Yoki12Num] [decimal](3, 0) NULL,
                [Yoki22Ryo] [decimal](5, 0) NULL,
                [Yoki22Num] [decimal](3, 0) NULL,
                [ShishinInfo] [decimal](7, 1) NULL,
                [Kuikomi] [decimal](4, 0) NULL,
                [ShiyoHanten] [decimal](4, 0) NULL,
                [KataChg] [decimal](4, 0) NULL,
                [RyoChg] [decimal](4, 0) NULL,
                [AllChg] [decimal](4, 0) NULL,
                [GasKireRiyu] [decimal](4, 0) NULL,
                [Ido] [decimal](9, 6) NULL,
                [Keido] [decimal](9, 6) NULL,
                [HaisoTenken1] [decimal](1, 0) NULL,
                [HaisoTenken2] [decimal](1, 0) NULL,
                [HaisoTenken3] [decimal](1, 0) NULL,
                [HaisoTenken4] [decimal](1, 0) NULL,
                [HaisoTenken5] [decimal](1, 0) NULL,
                [HaisoTenken6] [decimal](1, 0) NULL,
                [HaisoTenken7] [decimal](1, 0) NULL,
                [HaisoTenken8] [decimal](1, 0) NULL,
                [HaisoTenken9] [decimal](1, 0) NULL,
                [HaisoTenken10] [decimal](1, 0) NULL,
                [HaisoTenken11] [decimal](1, 0) NULL,
                [HaisoTenken12] [decimal](1, 0) NULL,
                [HaisoTenken13] [decimal](1, 0) NULL,
                [HaisoTenken14] [decimal](1, 0) NULL,
                [HaisoTenken15] [decimal](1, 0) NULL,
                [HaisoTenken16] [decimal](1, 0) NULL,
                [HaisoTenken17] [decimal](1, 0) NULL,
                [HaisoTenken18] [decimal](1, 0) NULL,
                [HaisoTenken19] [decimal](1, 0) NULL,
                [HaisoTenken20] [decimal](1, 0) NULL,
                [HaisoTenken21] [decimal](1, 0) NULL,
                [HaisoTenken22] [decimal](1, 0) NULL,
                [HaisoTenken23] [decimal](1, 0) NULL,
                [HaisoTenken24] [decimal](1, 0) NULL,
                [HaisoTenken25] [decimal](1, 0) NULL,
                [HaisoTenken26] [decimal](1, 0) NULL,
                [ShiyoZan] [decimal](7, 1) NULL,
                [YobiZan] [decimal](7, 1) NULL,
                [HaisoTime] [varchar](8) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                [MeterKekokuCheck] [decimal](1, 0) NULL,
                [MeterKekokuA] [decimal](1, 0) NULL,
                [MeterKekokuB] [decimal](1, 0) NULL,
                [MeterKekokuC] [decimal](1, 0) NULL,
                [MeterKekokuR] [decimal](1, 0) NULL,
                [created_at] [datetime] NULL,
                [updated_at] [datetime] NULL,
                PRIMARY KEY CLUSTERED
            (
                [id] ASC
            )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
            ) ON [PRIMARY];
        ";
        return $sql;
    }

    private function createHiyk($pdo)
    {
        $tbTemp = '#t_hiyk_temp';
        $sql = $this->sqlCreateHiyk($tbTemp);
        $sql .= $this->sqlCreateHiyk($tbTemp.'_1');
        // DB::statement($sql);
        $pdo->exec($sql);
        return $tbTemp;
    }

    private function sqlCreateHiyk($tbName){
        $sql = "
            CREATE TABLE {$tbName}(	
                [id] [bigint] IDENTITY(1,1) NOT NULL,
                [RandomString] [varchar](200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,			
                [KigyoCd] [int] NOT NULL,
                [HaisoCenterCd] [decimal](4, 0) NOT NULL,
                [HaisoTantCd] [decimal](8, 0) NULL,			
                [BusyoCd] [int] NOT NULL,			
                [EigyoCd] [int] NOT NULL,			
                [HanbaiCd] [decimal](4, 0) NOT NULL,			
                [BukkenNo] [varchar](12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,			
                [RirekiNo] [decimal](3, 0) NOT NULL,			
                [HaisoDt] [date] NULL,			
                [IdouKbn] [decimal](4, 0) NOT NULL,			
                [Barcode] [varchar](30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,			
                [HonKigo] [varchar](5) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,			
                [HonNo] [varchar](7) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,			
                [HikitoriKbn] [decimal](4, 0) NULL,			
                [SoshinDt] [datetime] NULL,			
                [created_at] [datetime] NULL,			
                [updated_at] [datetime] NULL,
                PRIMARY KEY CLUSTERED 
            (
                [id] ASC
            )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
            ) ON [PRIMARY];	
        ";
        return $sql;
    }

    private function createBulk($pdo)
    {
        $tbTemp = '#t_bulk_temp';
        $sql = $this->sqlCreateBulk($tbTemp);
        $sql .= $this->sqlCreateBulk($tbTemp.'_1');
        // DB::statement($sql);
        $pdo->exec($sql);
        return $tbTemp;
    }

    private function sqlCreateBulk($tbName){
        $sql = "
            CREATE TABLE {$tbName}(		
                [id] [bigint] IDENTITY(1,1) NOT NULL,	
                [RandomString] [varchar](200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,		
                [KigyoCd] [int] NOT NULL,
                [HaisoCenterCd] [decimal](4, 0) NOT NULL,
                [BusyoCd] [int] NOT NULL,
                [EigyoCd] [int] NOT NULL,
                [HanbaiCd] [decimal](4, 0) NOT NULL,
                [BukkenNo] [varchar](12) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
                [RirekiNo] [decimal](3, 0) NOT NULL,
                [HaisoDt] [date] NOT NULL,
                [IdouKbn] [decimal](4, 0) NOT NULL,
                [HaisoTantCd] [decimal](8, 0) NULL,
                [SechiKeitai] [decimal](4, 0) NULL,
                [Yoki11Ryo] [decimal](5, 0) NULL,
                [Yoki11Num] [decimal](3, 0) NULL,
                [Yoki21Ryo] [decimal](5, 0) NULL,
                [Yoki21Num] [decimal](3, 0) NULL,
                [Yoki12Ryo] [decimal](5, 0) NULL,
                [Yoki12Num] [decimal](3, 0) NULL,
                [Yoki22Ryo] [decimal](5, 0) NULL,
                [Yoki22Num] [decimal](3, 0) NULL,
                [ShishinInfo] [decimal](7, 1) NULL,
                [Ido] [decimal](9, 6) NULL,
                [Keido] [decimal](9, 6) NULL,
                [HaisoTenken1] [decimal](1, 0) NULL,
                [HaisoTenken2] [decimal](1, 0) NULL,
                [HaisoTenken3] [decimal](1, 0) NULL,
                [HaisoTenken4] [decimal](1, 0) NULL,
                [HaisoTenken5] [decimal](1, 0) NULL,
                [HaisoTenken6] [decimal](1, 0) NULL,
                [HaisoTenken7] [decimal](1, 0) NULL,
                [HaisoTenken8] [decimal](1, 0) NULL,
                [HaisoTenken9] [decimal](1, 0) NULL,
                [HaisoTenken10] [decimal](1, 0) NULL,
                [HaisoTenken11] [decimal](1, 0) NULL,
                [HaisoTenken12] [decimal](1, 0) NULL,
                [HaisoTenken13] [decimal](1, 0) NULL,
                [HaisoTenken14] [decimal](1, 0) NULL,
                [HaisoTenken15] [decimal](1, 0) NULL,
                [HaisoTenken16] [decimal](1, 0) NULL,
                [HaisoTenken17] [decimal](1, 0) NULL,
                [HaisoTenken18] [decimal](1, 0) NULL,
                [HaisoTenken19] [decimal](1, 0) NULL,
                [HaisoTenken20] [decimal](1, 0) NULL,
                [HaisoTenken21] [decimal](1, 0) NULL,
                [HaisoTenken22] [decimal](1, 0) NULL,
                [HaisoTenken23] [decimal](1, 0) NULL,
                [HaisoTenken24] [decimal](1, 0) NULL,
                [HaisoTenken25] [decimal](1, 0) NULL,
                [HaisoTenken26] [decimal](1, 0) NULL,
                [HaisoTime] [varchar](8) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
                [MeterKekokuCheck] [decimal](1, 0) NULL,
                [MeterKekokuA] [decimal](1, 0) NULL,
                [MeterKekokuB] [decimal](1, 0) NULL,
                [MeterKekokuC] [decimal](1, 0) NULL,
                [MeterKekokuR] [decimal](1, 0) NULL,
                [SyaryoCd] [decimal](4, 0) NOT NULL,
                [JyutenMa] [decimal](4, 1) NULL,
                [JyutenGo] [decimal](4, 1) NULL,
                [JyutenRyo] [decimal](8, 1) NULL,
                [created_at] [datetime] NULL,
                [updated_at] [datetime] NULL,
                PRIMARY KEY CLUSTERED 
            (
                [id] ASC
            )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
            ) ON [PRIMARY];	
        ";
        return $sql;
    }
}
