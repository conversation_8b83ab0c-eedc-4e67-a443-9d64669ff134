<?php

namespace App\Http\Controllers\Api;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Device;
use App\Models\Tant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Facades\JWTAuth;

class AuthController extends Controller
{
    private $logs = [];
    public function login(Request $request)
    {
        $codeLog404 = 'EM1003'; //codeLog
        $this->logs = ['url' => $request->url()];
        $validator = Validator::make($request->all(), [
            'UUID' => 'bail|required',
            'KigyoCd' => 'bail|required|integer',
            'TantCd' => 'bail|required|integer',
            'HandyPw' => 'bail|required'
        ]);
        $this->logs['request'] = $request->all();
        //Avoid data leaks
        if(isset($this->logs['request']['HandyPw'])) $this->logs['request']['HandyPw'] = "Hide password to avoid user data leakage";

        if ($validator->fails()) {
            $this->logs['message'] = $validator->messages()->all();
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            // $message = implode(', ', $validator->messages()->all());
            // return AppHelper::resError(400,AppHelper::$DEFAULT_400_MESS);
            return AppHelper::resError(404,'ユーザＩＤまたはパスワードが違います。');
        }

        $queryDevice = DB::table(AppHelper::$DEVICE)
            ->where('UUID',$request->UUID)
            ->where('KigyoCd',$request->KigyoCd)
            ->where('ActiveFlg',AppHelper::$ACTIVE);
        $device = $queryDevice->first();
        if(!$device) {
            $this->logs['message'] = 'Device not found.';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'有効なログイン情報ではありません。');
        }

        $queryUser = DB::table(AppHelper::$TANT)
            ->where('KigyoCd',$request->KigyoCd)
            ->where('TantCd',$request->TantCd)
            ->where('DisableFlg','<>',AppHelper::$DISABLE);
        $user = $queryUser->first();
        if(!$user) {
            $this->logs['message'] = 'User not found.';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'ユーザＩＤまたはパスワードが違います。');
        }

        if(!Hash::check($request->HandyPw, $user->HandyPw)){
            $ErrCnt = $user->ErrCnt != null ? ($user->ErrCnt + 1) : 1;
            if($ErrCnt > 8) $ErrCnt = 9;
            $userUpdate = $queryUser->update(['ErrCnt' => $ErrCnt, 'updated_at' => date('Y-m-d H:i:s')]);
            $this->logs['message'] = 'Password invalid.';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'ユーザＩＤまたはパスワードが違います。');
        }

        $now = strtotime('now');
        $lastLogin = $user->last_login?strtotime($user->last_login):0;
        if($user->ErrCnt >= AppHelper::$MAX_ERRCNT && ($lastLogin+AppHelper::$LOCK_TIME) >= $now) {
            $this->logs['message'] = 'User ErrCnt >= max errcnt';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'アカウントがロックされました。15分後にやり直してください。');
        }

        $arrLogin = [
            'KigyoCd' => $request->KigyoCd,
            'TantCd' => $request->TantCd,
            'password' => $request->HandyPw,
        ];
        $token = auth('api')->claims([
            'UUID' => $device->UUID,
        ])->attempt($arrLogin);
        if(!$token) {
            $this->logs['message'] = 'Token invalid.';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'Unknown error');
        }

        $last_login = date('Y-m-d H:i:s');
        $deviceUpdate = $queryDevice->update(['last_login' => $last_login, 'TantCd' => $user->TantCd, 'updated_at' => date('Y-m-d H:i:s')]);

        $userUpdate = $queryUser->update(['last_login' => $last_login, 'ErrCnt' => 0, 'updated_at' => date('Y-m-d H:i:s')]);
        return $this->resToken($token);
    }

    public function login2(Request $request)
    {
        $codeLog404 = 'EM1004'; //codeLog
        $codeLog400 = 'EM1005'; //codeLog
        $this->logs = ['url' => $request->url()];
        $validator = Validator::make($request->all(), [
            'KigyoCd' => 'bail|required|integer',
            'TantCd' => 'bail|required|integer',
            'HandyPw' => 'bail|required',
            'PassCd' => 'bail|nullable|string',
        ]);
        $this->logs['request'] = $request->all();
        // Avoid data leaks
        if(isset($this->logs['request']['HandyPw'])) $this->logs['request']['HandyPw'] = "Hide password to avoid user data leakage";
        if ($validator->fails()) {
            $this->logs['message'] = $validator->messages()->all();
            AppHelper::writeLog($this->logs, $codeLog400); //codeLog
            // $message = implode(', ', $validator->messages()->all());
             return AppHelper::resError(400,AppHelper::$DEFAULT_400_MESS);
            //            return AppHelper::resError(404,'ユーザＩＤまたはパスワードが違います。');
        }

        $queryUser = DB::table(AppHelper::$TANT)
            ->where('KigyoCd',$request->KigyoCd)
            ->where('TantCd',$request->TantCd)
            ->where('DisableFlg','<>',AppHelper::$DISABLE);
        $user = $queryUser->first();
        if(!$user) {
            $this->logs['message'] = 'User not found.';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'ユーザＩＤまたはパスワードが違います。');
        }

        if(!Hash::check($request->HandyPw, $user->HandyPw)){
            $ErrCnt = $user->ErrCnt != null ? ($user->ErrCnt + 1) : 1;
            if($ErrCnt > 8) $ErrCnt = 9;
            $userUpdate = $queryUser->update(['ErrCnt' => $ErrCnt, 'updated_at' => date('Y-m-d H:i:s')]);
            $this->logs['message'] = 'Password invalid.';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'ユーザＩＤまたはパスワードが違います。');
        }

        $now = strtotime('now');
        $lastLogin = $user->last_login?strtotime($user->last_login):0;
        if($user->ErrCnt >= AppHelper::$MAX_ERRCNT && ($lastLogin+AppHelper::$LOCK_TIME) >= $now) {
            $this->logs['message'] = 'User ErrCnt >= max errcnt';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'アカウントがロックされました。15分後にやり直してください。');
        }
        $arrLogin = [
            'KigyoCd' => $request->KigyoCd,
            'TantCd' => $request->TantCd,
            'password' => $request->HandyPw,
        ];
        if($user->factor_cert_flag != 0){
            $insertToken = false;
            for($i = 0; $i <= AppHelper::$numErrorWhenGenToken; $i++){
                $insertToken = AppHelper::insertToken($arrLogin['KigyoCd']);
                if($insertToken) break;
            }
            if(!$insertToken){
                $this->logs['message'] = 'can not insert token to m_device';
                AppHelper::writeLog($this->logs, $codeLog404); //codeLog
                return AppHelper::resError(404,'ユーザＩＤまたはパスワードが違います。');
            }

            return $this->getTokenLogin2($arrLogin, $queryUser, $insertToken, $codeLog404);
        }

        if(!isset($request->PassCd) || $request->PassCd == null || $request->PassCd == ''){
            $this->logs['message'] = 'User factor_cert_flag = 0 but do not have PassCd';
            AppHelper::writeLog($this->logs, $codeLog400); //codeLog
            return AppHelper::resError(401,'二要素認証必要です。');
        }

        if($request->PassCd != $user->factor_qr_code){
            $this->logs['message'] = 'PassCd != factor_qr_code';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'認証カードのQRコードが違います。');
        }

        $insertToken = false;
        for($i = 0; $i <=  AppHelper::$numErrorWhenGenToken; $i++){
            $insertToken = AppHelper::insertToken($arrLogin['KigyoCd']);
            if($insertToken) break;
        }
        if(!$insertToken){
            $this->logs['message'] = 'can not insert token to m_device';
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404,'ユーザＩＤまたはパスワードが違います。');
        }
        return $this->getTokenLogin2($arrLogin, $queryUser, $insertToken, $codeLog404);
    }

    private function getTokenLogin2($arrLogin,$queryUser, $insertToken, $codeLog)
    {
//        $token = auth('api')->attempt($arrLogin);
        $token = auth('api')->claims([
            'UUID' => $insertToken,
        ])->attempt($arrLogin);
        if(!$token) {
            $this->logs['message'] = 'Token invalid.';
            AppHelper::writeLog($this->logs, $codeLog); //hàm cha sử dụng khai báo $this->logs = ['url' => $request->url(), 'request' => $request->all(),];
            return AppHelper::resError(404,'Unknown error');
        }
        $last_login = date('Y-m-d H:i:s');
        $userUpdate = $queryUser->update(['last_login' => $last_login, 'ErrCnt' => 0, 'updated_at' => date('Y-m-d H:i:s')]);

        return $this->resToken($token);
    }

    public function logout()
    {
//        auth('api')->logout();
        AppHelper::actionLogout();
        return response([
            'success' => true
        ]);
    }

    // public function register(Request $request)
    // {
    //     $validator = Validator::make($request->all(), [
    //         'KigyoCd' => 'bail|required|integer|unique:' . AppHelper::$TANT . ',KigyoCd,NULL,KigyoCd,TantCd,'.$request->TantCd,
    //         'TantCd' => 'bail|required|integer|between:0,99999999',
    //         'Name' => 'bail|required|string|max:50',
    //         'HandyPw' => 'bail|required|string|max:50',
    //     ]);
    //     if ($validator->fails()) {
    //     //            $message = implode(', ', $validator->messages()->all());
    //         return AppHelper::resError(400,AppHelper::$DEFAULT_400_MESS);
    //     }

    //     $user = Tant::create([
    //         'KigyoCd' => $request->KigyoCd,
    //         'TantCd' => $request->TantCd,
    //         'Name' => $request->Name,
    //         'HandyPw' => Hash::make($request->HandyPw),
    //         'ErrCnt' => 0,
    //         'DisableFlg' => AppHelper::$NOT_DISABLE,
    //     ]);
    //     return response([
    //         'code' => 200,
    //         'message' => 'Success',
    //         'user' => $user
    //     ],200);
    // }

    public function regist(Request $request)
    {
        $codeLog400 = 'EM1001'; //codeLog
        $codeLog404 = 'EM1002'; //codeLog
        $this->logs = ['url' => $request->url(), 'request' => $request->all(),];
        $validator = Validator::make($request->all(), [
            'UUID' => 'bail|required|string|max:64',
            'KigyoCd' => 'bail|required|integer',
            'DeviceName' => 'bail|required|string|max:50',
            'ActiveFlg' => 'bail|nullable|integer|in:0,1',
        ]);
        if ($validator->fails()) {
            // $message = implode(', ', $validator->messages()->all());
            $this->logs['message'] = $validator->messages()->all();
            AppHelper::writeLog($this->logs, $codeLog400); //codeLog
            return AppHelper::resError(400,AppHelper::$DEFAULT_400_MESS);
        }

        if (!Company::where('company_id', $request->KigyoCd)->where('db_status', 1)->exists()) {
            $message = '企業コード は存在しません。';
            $this->logs['message'] = $message;
            AppHelper::writeLog($this->logs, $codeLog404); //codeLog
            return AppHelper::resError(404, $message);
        }
        $data = ['DeviceName' => $request->DeviceName];
        if(isset($request->ActiveFlg))
            $data['ActiveFlg'] = $request->ActiveFlg;

        $data['updated_at'] = date('Y-m-d H:i:s');
        $device = DB::table(AppHelper::$DEVICE)->updateOrInsert(['UUID' => $request->UUID, 'KigyoCd' => $request->KigyoCd], $data);
        $device = DB::table(AppHelper::$DEVICE)
            ->where('UUID',$request->UUID)
            ->where('KigyoCd',$request->KigyoCd)
            ->first();
        return response([
            'code' => 200,
            'message' => 'Success',
            'device' => [
                'DeviceName' => $device->DeviceName,
                'ActiveFlg' => is_null($device->ActiveFlg)?0:$device->ActiveFlg,
            ]
        ],200);
    }

    // public function getdevice(Request $request)
    // {
    //     $validator = Validator::make($request->all(), [
    //         'UUID' => 'bail|required|string|max:64',
    //     ]);
    //     if ($validator->fails()) {
    //         // $message = implode(', ', $validator->messages()->all());
    //         return AppHelper::resError(400,AppHelper::$DEFAULT_400_MESS);
    //     }
    //     $device = Device::where('UUID',$request->UUID)->first();
    //     if(!$device)
    //         return AppHelper::resError(404,'UUIDは存在していないです。');
    //     return response([
    //         'code' => 200,
    //         'message' => 'Success',
    //         'device' => [
    //             'KigyoCd' => $device->KigyoCd,
    //             'DeviceName' => $device->DeviceName,
    //             'ActiveFlg' => is_null($device->ActiveFlg)?0:$device->ActiveFlg,
    //         ]
    //     ],200);
    // }

    /**
     * Get the authenticated User.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    // public function user() {
    //     $arr = [
    //         'user' => auth('api')->user(),
    //         'UUID' => AppHelper::getLoginUUID()
    //     ];
    //     return response()->json($arr);
    // }
    /**
     * Get the token array structure.
     *
     * @param  string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function resToken($token)
    {
        return response()->json([
            'code' => 200,
            'message' => 'Success',
            'user' => auth('api')->user(),
            'token' => $token,

            // 'token_type' => 'bearer',
            // 'expires_in' => auth('api')->factory()->getTTL() * 60,
            // 'user' => auth('api')->user(),
            // 'UUID' => AppHelper::getLoginUUID(),
        ]);
    }
}
