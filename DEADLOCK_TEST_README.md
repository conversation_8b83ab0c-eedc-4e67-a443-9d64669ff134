# Deadlock Test Scripts

Bộ script PHP để test deadlock cho API upload c<PERSON><PERSON> application.

## Files

1. **`deadlock_test_script.php`** - Class chính với đầy đủ tính năng
2. **`run_deadlock_test.php`** - Script chạy test tự động (cần pcntl extension)
3. **`simple_test.php`** - <PERSON><PERSON>t đơn giản, không cần pcntl (chạy được trên Windows)
4. **`DEADLOCK_TEST_README.md`** - File hướng dẫn này

## Cấu hình

### 1. Cập nhật thông tin kết nối

Sửa các biến sau trong scripts:

```php
$BASE_URL = 'http://localhost:8000'; // URL của <PERSON> app
$LOGIN_DATA = [
    'KigyoCd' => 22408,
    'TantCd' => 1,
    'HandyPw' => 'password123' // Password thực tế
];
```

### 2. <PERSON><PERSON><PERSON> bảo user tồn tại trong database

Kiểm tra trong bảng `m_tant` có user với:
- <PERSON>gy<PERSON>Cd = 22408
- TantCd = 1
- HandyPw = hash của 'password123'
- DisableFlg != 1

## Cách sử dụng

### Option 1: Script đơn giản (Khuyến nghị cho Windows)

```bash
php simple_test.php
```

**Tính năng:**
- Test single upload
- Test sequential uploads (simulate concurrent load)
- Không cần pcntl extension
- Chạy được trên Windows
- Dễ debug và modify

### Option 2: Script đầy đủ (Linux/Mac với pcntl)

```bash
php run_deadlock_test.php
```

**Tính năng:**
- Test concurrent uploads thực sự (multi-process)
- Stress testing với nhiều process đồng thời
- Logging chi tiết
- Cần pcntl extension

### Option 3: Custom test

```php
require_once 'deadlock_test_script.php';

$config = [
    'base_url' => 'http://your-domain.com',
    'login' => [
        'KigyoCd' => 12345,
        'TantCd' => 999,
        'HandyPw' => 'your-password'
    ]
];

$tester = new DeadlockTester($config);
$tester->login();

// Test với dữ liệu custom
$customData = [
    'upload' => [
        [
            'fileType' => '30_HISO',
            'datas' => [
                '22408,0,0,1,000063115001,1,20250529,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0.0,0.0,10:41:40,0,0,0,0,0'
            ]
        ]
    ]
];

$result = $tester->upload($customData);
```

## Các loại test

### 1. Single Upload Test
- Test 1 request đơn lẻ
- Verify API hoạt động bình thường
- Kiểm tra response format

### 2. Sequential Upload Test
- Nhiều request liên tiếp
- Simulate load cao
- Test retry logic

### 3. Concurrent Upload Test (chỉ với pcntl)
- Nhiều process chạy đồng thời
- Test deadlock thực sự
- Stress testing

## Dữ liệu test

### HISO Data (30_HISO)
```
KigyoCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,
Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,
ShishinInfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,
HaisoTenken1-26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR
```

### HIYK Data (31_HIYK)
```
KigyoCd,HaisoTantCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,
Barcode,HonKigo,HonNo,HikitoriKbn,SoshinDt
```

### BULK Data (33_BULK)
```
KigyoCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,
Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,
ShishinInfo,Ido,Keido,HaisoTenken1-26,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,
SyaryoCd,JyutenMa,JyutenGo,JyutenRyo
```

## Monitoring

### Log Files
- `deadlock_test_YYYY-MM-DD_HH-mm-ss.log` - Chi tiết từng request
- Console output - Tóm tắt kết quả

### Kiểm tra deadlock trong SQL Server
```sql
-- Monitor active sessions
SELECT 
    session_id,
    blocking_session_id,
    wait_type,
    wait_time,
    wait_resource,
    command
FROM sys.dm_exec_requests 
WHERE blocking_session_id <> 0;

-- Deadlock graph (nếu có)
SELECT 
    XEvent.query('(event/data/value/deadlock)[1]') as DeadlockGraph
FROM (
    SELECT XEvent.query('.') as XEvent
    FROM (
        SELECT CAST(target_data as xml) as TargetData
        FROM sys.dm_xe_session_targets st
        JOIN sys.dm_xe_sessions s ON s.address = st.event_session_address
        WHERE name = 'system_health'
    ) AS Data
    CROSS APPLY TargetData.nodes ('//RingBufferTarget/event') AS XEventData (XEvent)
) as src
WHERE XEvent.exist('//event[@name="xml_deadlock_report"]') = 1;
```

## Troubleshooting

### Lỗi thường gặp

1. **"Login failed"**
   - Kiểm tra URL base
   - Verify user credentials trong database
   - Check user không bị disable

2. **"CURL Error"**
   - Kiểm tra network connectivity
   - Verify SSL settings
   - Check firewall

3. **"pcntl extension required"**
   - Sử dụng `simple_test.php` thay vì `run_deadlock_test.php`
   - Hoặc install pcntl extension

4. **"Upload failed"**
   - Check token expiry
   - Verify data format
   - Review Laravel logs

### Debug tips

1. **Enable verbose logging:**
   ```php
   curl_setopt($ch, CURLOPT_VERBOSE, true);
   ```

2. **Check Laravel logs:**
   ```bash
   tail -f storage/logs/laravel.log
   ```

3. **Monitor SQL Server:**
   ```sql
   SELECT * FROM sys.dm_exec_requests WHERE session_id > 50;
   ```

## Kết quả mong đợi

### Thành công
- Tất cả requests return code 200
- Không có deadlock errors
- Response time ổn định

### Deadlock được xử lý
- Một số requests có thể retry
- Cuối cùng vẫn thành công
- Log ghi nhận retry attempts

### Cần điều tra
- Requests failed liên tục
- Response time tăng cao
- Deadlock không được resolve
