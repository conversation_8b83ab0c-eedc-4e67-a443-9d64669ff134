<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTants extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tants', function (Blueprint $table) {
            $table->id();
            $table->string('KigyoCd');
            $table->string('TantCd');
            $table->tinyInteger('KenshinTantFlg')->default(0);
            $table->tinyInteger('HaisoTantFlg')->default(0);
            $table->string('KyotenCd')->nullable();
            $table->string('HaisoCenterCd')->nullable();
            $table->string('HandyKengen')->nullable();
            $table->string('Name');
            $table->string('HandyPw');
            $table->rememberToken();
            $table->timestamps();
            $table->unique(['KigyoCd', 'TantCd']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tants');
    }
}
