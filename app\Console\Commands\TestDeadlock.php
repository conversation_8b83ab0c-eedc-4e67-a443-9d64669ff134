<?php

namespace App\Console\Commands;

use Illuminate\Support\Facades\DB;
use Illuminate\Console\Command;

class TestDeadlock extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:deadlock';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // $RandomString = "1748230371-Qcq9myoCxjX3uFJuXzYpuSJgo4QRved6x9czulkDuic3cOUTEqeCJBxKwLjO6nvcpNiAuntMULjVCi3RIPdIlFaRIK_test";
        $RandomString = "1748230371-Qcq9myoCxjX3uFJuXzYpuSJgo4QRved6x9czulkDuic3cOUTEqeCJBxKwLjO6nvcpNiAuntMULjVCi3RIPdIlFaRIK";
        DB::beginTransaction();
        try {

            echo ("\nStart step 1:INSERT INTO t_hiso_temp_1 FROM t_hiso_temp:--- \n" );
            // MERGE INTO t_hiso_temp_1 AS target
            DB::statement("
            MERGE INTO t_hiso_temp_1 WITH (HOLDLOCK, ROWLOCK)  AS target
            USING (
                SELECT *
                FROM t_hiso_temp
                WHERE RandomString = ?
            ) AS source
            ON target.KigyoCd = source.KigyoCd
            AND target.HaisoCenterCd = source.HaisoCenterCd
            AND target.BusyoCd = source.BusyoCd
            AND target.EigyoCd = source.EigyoCd
            AND target.HanbaiCd = source.HanbaiCd
            AND target.BukkenNo = source.BukkenNo
            AND target.RirekiNo = source.RirekiNo
            AND target.HaisoDt = source.HaisoDt
            WHEN NOT MATCHED THEN
                INSERT (
                    RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,
                    Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,
                    KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,
                    HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,
                    HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,
                    HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,
                    HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,
                    MeterKekokuC,MeterKekokuR,created_at,updated_at
                )
                VALUES (
                    source.RandomString,source.KigyoCd,source.HaisoCenterCd,source.BusyoCd,source.EigyoCd,source.HanbaiCd,
                    source.BukkenNo,source.RirekiNo,source.HaisoDt,source.IdouKbn,source.HaisoTantCd,source.SechiKeitai,
                    source.Yoki11Ryo,source.Yoki11Num,source.Yoki21Ryo,source.Yoki21Num,source.Yoki12Ryo,source.Yoki12Num,
                    source.Yoki22Ryo,source.Yoki22Num,source.Shishininfo,source.Kuikomi,source.ShiyoHanten,
                    source.KataChg,source.RyoChg,source.AllChg,source.GasKireRiyu,source.Ido,source.Keido,
                    source.HaisoTenken1,source.HaisoTenken2,source.HaisoTenken3,source.HaisoTenken4,source.HaisoTenken5,
                    source.HaisoTenken6,source.HaisoTenken7,source.HaisoTenken8,source.HaisoTenken9,source.HaisoTenken10,
                    source.HaisoTenken11,source.HaisoTenken12,source.HaisoTenken13,source.HaisoTenken14,source.HaisoTenken15,
                    source.HaisoTenken16,source.HaisoTenken17,source.HaisoTenken18,source.HaisoTenken19,source.HaisoTenken20,
                    source.HaisoTenken21,source.HaisoTenken22,source.HaisoTenken23,source.HaisoTenken24,source.HaisoTenken25,
                    source.HaisoTenken26,source.ShiyoZan,source.YobiZan,source.HaisoTime,source.MeterKekokuCheck,
                    source.MeterKekokuA,source.MeterKekokuB,source.MeterKekokuC,source.MeterKekokuR,source.created_at,source.updated_at
                );

            ", [$RandomString]);
            sleep(3); //
            echo ("\nEnd step 1:\n" );


            // // Step 1: INSERT INTO t_hiso_temp_1 FROM t_hiso_temp
            // echo ("\nStart step 1:INSERT INTO t_hiso_temp_1 FROM t_hiso_temp:--- \n" );
            // DB::statement("
            // INSERT INTO t_hiso_temp_1 (RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at)
            // SELECT RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at
            // FROM t_hiso_temp
            // WHERE RandomString = ?
            // ", [$RandomString]);
            // sleep(3); //
            // echo ("\nEnd step 1:\n" );
            

            // echo (date('H:m:s'));
            // echo ("\nStart step 2:Remove duplicates in t_hiso_temp_1:--- \n" );
            // DB::statement("
            //      WITH cte AS (
            //         SELECT 
            //             KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at,
            //             ROW_NUMBER() OVER (
            //                 PARTITION BY 
            //                     KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
            //                 ORDER BY 
            //                     KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
            //             ) row_num
            //         FROM 
            //             t_hiso_temp_1
            //         WHERE 
            //             RandomString = ?
            //         )
            //     DELETE FROM cte
            //     WHERE row_num > 1;
            // ", [$RandomString]);
            // sleep(5); //
            // echo ("\nEnd step 2:\n" );

            //  echo (date('H:m:s'));
            // echo ("\nStart step 2.2:Remove in t_hiso_temp_1:--- \n" );
            // DB::statement("
            //     DELETE FROM t_hiso_temp_1
            //     WHERE RandomString = ?;
            // ", [$RandomString]);
            // sleep(5); //
            // echo ("\nEnd step 2:\n" );


            echo ("\nStart step 3:INSERT INTO t_hiso if not exists:--- \n" );
            DB::statement("
                  INSERT INTO t_hiso SELECT t2.KigyoCd,t2.HaisoCenterCd,t2.BusyoCd,t2.EigyoCd,t2.HanbaiCd,t2.BukkenNo,t2.RirekiNo,t2.HaisoDt,t2.IdouKbn,t2.HaisoTantCd,t2.SechiKeitai,t2.Yoki11Ryo,t2.Yoki11Num,t2.Yoki21Ryo,t2.Yoki21Num,t2.Yoki12Ryo,t2.Yoki12Num,t2.Yoki22Ryo,t2.Yoki22Num,t2.Shishininfo,t2.Kuikomi,t2.ShiyoHanten,t2.KataChg,t2.RyoChg,t2.AllChg,t2.GasKireRiyu,t2.Ido,t2.Keido,t2.HaisoTenken1,t2.HaisoTenken2,t2.HaisoTenken3,t2.HaisoTenken4,t2.HaisoTenken5,t2.HaisoTenken6,t2.HaisoTenken7,t2.HaisoTenken8,t2.HaisoTenken9,t2.HaisoTenken10,t2.HaisoTenken11,t2.HaisoTenken12,t2.HaisoTenken13,t2.HaisoTenken14,t2.HaisoTenken15,t2.HaisoTenken16,t2.HaisoTenken17,t2.HaisoTenken18,t2.HaisoTenken19,t2.HaisoTenken20,t2.HaisoTenken21,t2.HaisoTenken22,t2.HaisoTenken23,t2.HaisoTenken24,t2.HaisoTenken25,t2.HaisoTenken26,t2.ShiyoZan,t2.YobiZan,t2.HaisoTime,t2.MeterKekokuCheck,t2.MeterKekokuA,t2.MeterKekokuB,t2.MeterKekokuC,t2.MeterKekokuR,t2.created_at,t2.updated_at FROM t_hiso_temp_1 t2
                LEFT JOIN t_hiso t1 ON t1.KigyoCd = t2.KigyoCd AND t1.HaisoCenterCd = t2.HaisoCenterCd AND t1.BusyoCd = t2.BusyoCd AND t1.EigyoCd = t2.EigyoCd AND t1.HanbaiCd = t2.HanbaiCd AND t1.BukkenNo = t2.BukkenNo AND t1.RirekiNo = t2.RirekiNo AND t1.HaisoDt = t2.HaisoDt
                WHERE t1.KigyoCd IS NULL AND t1.HaisoCenterCd IS NULL AND t1.BusyoCd IS NULL AND t1.EigyoCd IS NULL AND t1.HanbaiCd IS NULL AND t1.BukkenNo IS NULL AND t1.RirekiNo IS NULL AND t1.HaisoDt IS NULL AND t2.RandomString = ?
            ", [$RandomString]);
            sleep(5); //
            echo ("\nEnd step 3:\n" );

             echo ("\nStart step 4:DELETE from t_hiso_temp keep MAX(id):--- \n" );
            // DB::statement("
            //      DELETE FROM t_hiso_temp WHERE id NOT IN (
            //         SELECT id FROM (
            //             SELECT MAX(id) as id,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt 
            //             FROM t_hiso_temp
            //             WHERE RandomString = ?
            //             GROUP BY KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
            //         ) 
            //         AS t1
            //     );
            // ", [$RandomString]);
            echo ("\nEnd step 4:\n" );

             echo ("\nStart step 5:UPDATE t_hiso from t_hiso_temp:--- \n" );
            DB::statement("
                 UPDATE t1 
            SET t1.IdouKbn = t2.IdouKbn,t1.HaisoTantCd = t2.HaisoTantCd,t1.SechiKeitai = t2.SechiKeitai,t1.Yoki11Ryo = t2.Yoki11Ryo,t1.Yoki11Num = t2.Yoki11Num,t1.Yoki21Ryo = t2.Yoki21Ryo,t1.Yoki21Num = t2.Yoki21Num,t1.Yoki12Ryo = t2.Yoki12Ryo,t1.Yoki12Num = t2.Yoki12Num,t1.Yoki22Ryo = t2.Yoki22Ryo,t1.Yoki22Num = t2.Yoki22Num,t1.Shishininfo = t2.Shishininfo,t1.Kuikomi = t2.Kuikomi,t1.ShiyoHanten = t2.ShiyoHanten,t1.KataChg = t2.KataChg,t1.RyoChg = t2.RyoChg,t1.AllChg = t2.AllChg,t1.GasKireRiyu = t2.GasKireRiyu,t1.Ido = t2.Ido,t1.Keido = t2.Keido,t1.HaisoTenken1 = t2.HaisoTenken1,t1.HaisoTenken2 = t2.HaisoTenken2,t1.HaisoTenken3 = t2.HaisoTenken3,t1.HaisoTenken4 = t2.HaisoTenken4,t1.HaisoTenken5 = t2.HaisoTenken5,t1.HaisoTenken6 = t2.HaisoTenken6,t1.HaisoTenken7 = t2.HaisoTenken7,t1.HaisoTenken8 = t2.HaisoTenken8,t1.HaisoTenken9 = t2.HaisoTenken9,t1.HaisoTenken10 = t2.HaisoTenken10,t1.HaisoTenken11 = t2.HaisoTenken11,t1.HaisoTenken12 = t2.HaisoTenken12,t1.HaisoTenken13 = t2.HaisoTenken13,t1.HaisoTenken14 = t2.HaisoTenken14,t1.HaisoTenken15 = t2.HaisoTenken15,t1.HaisoTenken16 = t2.HaisoTenken16,t1.HaisoTenken17 = t2.HaisoTenken17,t1.HaisoTenken18 = t2.HaisoTenken18,t1.HaisoTenken19 = t2.HaisoTenken19,t1.HaisoTenken20 = t2.HaisoTenken20,t1.HaisoTenken21 = t2.HaisoTenken21,t1.HaisoTenken22 = t2.HaisoTenken22,t1.HaisoTenken23 = t2.HaisoTenken23,t1.HaisoTenken24 = t2.HaisoTenken24,t1.HaisoTenken25 = t2.HaisoTenken25,t1.HaisoTenken26 = t2.HaisoTenken26,t1.ShiyoZan = t2.ShiyoZan,t1.YobiZan = t2.YobiZan,t1.HaisoTime = t2.HaisoTime,t1.MeterKekokuCheck = t2.MeterKekokuCheck,t1.MeterKekokuA = t2.MeterKekokuA,t1.MeterKekokuB = t2.MeterKekokuB,t1.MeterKekokuC = t2.MeterKekokuC,t1.MeterKekokuR = t2.MeterKekokuR,t1.updated_at = t2.updated_at 
            FROM t_hiso t1, t_hiso_temp t2 
            WHERE 
                t1.KigyoCd = t2.KigyoCd AND t1.HaisoCenterCd = t2.HaisoCenterCd AND t1.BusyoCd = t2.BusyoCd AND t1.EigyoCd = t2.EigyoCd AND t1.HanbaiCd = t2.HanbaiCd AND t1.BukkenNo = t2.BukkenNo AND t1.RirekiNo = t2.RirekiNo AND t1.HaisoDt = t2.HaisoDt AND t2.RandomString = ?;
            ", [$RandomString]);
            sleep(5); //
            echo ("\nEnd step 5:\n" );

            // Step 6: DELETE all temp
            // DB::statement("DELETE FROM t_hiso_temp WHERE RandomString = ?", [$RandomString]);
            // DB::statement("DELETE FROM t_hiso_temp_1 WHERE RandomString = ?", [$RandomString]);

            DB::commit();
            $this->info('done.');
        } catch (\Exception $th) {
            DB::rollBack();
            $this->error("Error: " . $th->getMessage(). $th->getLine());
        }

    }
}
