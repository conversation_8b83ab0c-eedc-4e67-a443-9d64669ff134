<?php

namespace App\Http\Controllers\Api;

use App\Helpers\AppHelper;
use App\Http\Controllers\Controller;
use App\Models\Bbkn;
use App\Models\Code;
use App\Models\Hbkn;
use App\Models\Hsyk;
use App\Models\Srmk;
use Illuminate\Support\Facades\DB;

class DownloadController extends Controller
{
    private $res = [
        'code' => 0,
        'message' => 'Success',
        'download' => []
    ];

    public function dload()
    {
        $this->getData('21_HSYK');
        $this->getData('20_HBKN');
        $this->getData('41_HSYK');
        $this->getData('40_HBKN');
        return response($this->res);
    }

    public function dloadblk()
    {
        $this->getData('23_BBKN');
        $this->getData('43_BBKN');
        return response($this->res);
    }

    public function master()
    {
        $this->getData('11_SRMK');
        $this->getData('01_CODE');
        return response($this->res);
    }

    private function getData($fileType)
    {
        $arr = [
            'fileType' => $fileType,
            'datas' => [],
        ];
        $datas = $this->actionGetDatas($fileType);
        if(is_null($datas))
            return;

        $arr['datas'] = $datas;
        $this->res['download'][] = $arr;
    }

    private function actionGetDatas($fileType)
    {
        $user = auth('api')->user();
        $data = null;
        switch ($fileType){
            case '21_HSYK':
                $datas = Hsyk::insertDownload()
                    ->whereTantCdOrNull($user->TantCd)
                    ->where('HaisoCenterCd',$user->HaisoCenterCd)
                    ->where('KigyoCd',$user->KigyoCd)
                    ->get()
                    ->toArray();
                $selectCols = Hsyk::$selectColsApiDownload;
                $delCols = Hsyk::$deleteColsApiDownload;
                $data = $this->getResData(AppHelper::$HSYK,$datas,$selectCols,$delCols,false);
                break;
            case '41_HSYK':
                $datas = Hsyk::where('update_flg', AppHelper::$isUpdate)
                    ->whereTantCdOrNull($user->TantCd)
                    ->where('HaisoCenterCd',$user->HaisoCenterCd)
                    ->where('KigyoCd',$user->KigyoCd)
                    ->get()
                    ->toArray();
                $selectCols = Hsyk::$selectColsApiDownload;
                $delCols = Hsyk::$deleteColsApiDownload;
                $data = $this->getResData(AppHelper::$HSYK,$datas,$selectCols,$delCols,true);
                break;
            case '20_HBKN':
                $datas = Hbkn::insertDownload()
                    ->whereTantCdOrNull($user->TantCd)
                    ->where('HaisoCenterCd',$user->HaisoCenterCd)
                    ->where('KigyoCd',$user->KigyoCd)
                    ->get()
                    ->toArray();
                $selectCols = Hbkn::$selectColsApiDownload;
                $delCols = Hbkn::$deleteColsApiDownload;
                $data = $this->getResData(AppHelper::$HBKN,$datas,$selectCols,$delCols,false);
                break;
            case '40_HBKN':
                $datas = Hbkn::where('update_flg', AppHelper::$isUpdate)
                    ->whereTantCdOrNull($user->TantCd)
                    ->where('HaisoCenterCd',$user->HaisoCenterCd)
                    ->where('KigyoCd',$user->KigyoCd)
                    ->get()
                    ->toArray();
                $selectCols = Hbkn::$selectColsApiDownload;
                $delCols = Hbkn::$deleteColsApiDownload;
                $data = $this->getResData(AppHelper::$HBKN,$datas,$selectCols,$delCols,true);
                break;
            case '23_BBKN':
                $datas = Bbkn::insertDownload()
                    ->whereTantCdOrNull($user->TantCd)
                    ->where('HaisoCenterCd',$user->HaisoCenterCd)
                    ->where('KigyoCd',$user->KigyoCd)
                    ->get()
                    ->toArray();
                $selectCols = Bbkn::$selectColsApiDownload;
                $delCols = Bbkn::$deleteColsApiDownload;
                $data = $this->getResData(AppHelper::$BBKN,$datas,$selectCols,$delCols,false);
                break;
            case '43_BBKN':
                $datas = Bbkn::where('update_flg', AppHelper::$isUpdate)
                    ->whereTantCdOrNull($user->TantCd)
                    ->where('HaisoCenterCd',$user->HaisoCenterCd)
                    ->where('KigyoCd',$user->KigyoCd)
                    ->get()
                    ->toArray();
                $selectCols = Bbkn::$selectColsApiDownload;
                $delCols = Bbkn::$deleteColsApiDownload;
                $data = $this->getResData(AppHelper::$BBKN,$datas,$selectCols,$delCols,true);
                break;
            case '11_SRMK':
                $datas = Srmk::selectColumnsDownload()
                    ->where('KigyoCd',$user->KigyoCd)
                    ->get()
                    ->toArray();
                if(!count($datas))
                    $data = null;
                foreach ($datas as $tData){
                    $replacedData = array_map(function ($v) {
                        return str_replace(',','、',$v);
                    }, $tData);
                    $data[] = implode(',',$replacedData);
                }
                break;
            case '01_CODE':
                $datas = Code::selectColumnsDownload()
                    ->where('KigyoCd',$user->KigyoCd)
                    ->get()
                    ->toArray();
                if(!count($datas))
                    $data = null;
                foreach ($datas as $tData){
                    $replacedData = array_map(function ($v) {
                        return str_replace(',','、',$v);
                    }, $tData);
                    $data[] = implode(',',$replacedData);
                }
                break;
        }
        return $data;
    }

    private function getResData($table,$datas,$selectCols,$delCols,$updateFlag = false)
    {
        if(!count($datas))
            return null;
        $res = [];
        $delSql = '';
        $i = 1;
        $tData = array_map(function ($val){return null;},array_flip($selectCols));
        foreach ($datas as $data){
            foreach ($tData as $k => $v){
                $tData[$k] = str_replace(',','、',$data[$k]);
            }
            $res[] = implode(',',$tData);

            $where = ' WHERE ';
            foreach ($delCols as $v) {
                if ($v != 'HaisoTantCd')
                    $where .= $v . " = '" . $data[$v] . "' AND ";
                else
                    $where .= "(" . $v . " = '" . $data[$v] . "' OR " . $v . " IS NULL) AND ";
            }
            if($updateFlag)
                $where .= "update_flg = '".AppHelper::$isUpdate."';";
            else
                $where .= "(update_flg <> '".AppHelper::$isUpdate."' OR update_flg IS NULL);";
            $delSql.= "DELETE FROM ".$table.$where;
            $i++;
            if($i >= 1000 && $delSql){
                DB::statement($delSql);
                $delSql = '';
                $i = 1;
            }
        }
        if($delSql)
            DB::statement($delSql);
        return $res;
    }
}
