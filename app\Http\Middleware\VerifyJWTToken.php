<?php

namespace App\Http\Middleware;

use App\Helpers\AppHelper;
use App\Models\Device;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Ty<PERSON>\JWTAuth\Exceptions\JWTException;
use <PERSON>mon\JWTAuth\Exceptions\TokenExpiredException;
use Tymon\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Facades\JWTAuth;

class VerifyJWTToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            $user = auth('api')->user();

            if(!$user || $user->DisableFlg == AppHelper::$DISABLE)
            {
                AppHelper::actionLogout();
                return AppHelper::resError(401,'有効なトークンではありません。');
            }
            $uuid = AppHelper::getLoginUUID();
            $queryDevice = DB::table(AppHelper::$DEVICE)
                ->where('UUID',$uuid)
                ->where('KigyoCd',$user->KigyoCd);
            $device = $queryDevice->first();
            if(
               !$device
//                || $device->ActiveFlg == AppHelper::$NOT_ACTIVE
//                || $device->ActiveFlg == AppHelper::$DEVICE_DISABLE
            ) {
                AppHelper::actionLogout();
                return AppHelper::resError(401,'有効なトークンではありません。');
            }

            $now = strtotime('now');
            if(strtotime($device->last_login) + AppHelper::$SLEEP_TIME <= $now){
                AppHelper::actionLogout();
                return AppHelper::resError(408,'トークンタイムアウトです。');
            }
            $deviceUpdate = $queryDevice->update(['last_login' => date('Y-m-d H:i:s'), 'updated_at' => date('Y-m-d H:i:s')]);

            $minuteToken = env("JWT_TTL", 60);
            $dateExpired = date('Y-m-d H:i:s',strtotime("- {$minuteToken} minutes"));
            $deviceDelete = DB::table(AppHelper::$DEVICE)
                ->where('DeviceName', '=',AppHelper::$deviceNameWhenGenToken)
                ->where('last_login', '<', $dateExpired)
                ->delete();

        }catch (JWTException $e) {
//            if($e instanceof TokenExpiredException)
                return AppHelper::resError(408,'トークンタイムアウトです。');
//            else
//                return AppHelper::resError(401,'有効なトークンではありません。');
//            if($e instanceof TokenExpiredException) {
//                return response()->json(['token_expired'], $e->getStatusCode());
//            }else if ($e instanceof TokenInvalidException) {
//                return response()->json(['token_invalid'], $e->getStatusCode());
//            }else{
//                return response()->json(['error'=>'Token is required']);
//            }
        }
        return $next($request);
    }
}
