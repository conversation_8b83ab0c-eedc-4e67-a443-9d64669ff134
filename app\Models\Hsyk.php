<?php

namespace App\Models;

use App\Helpers\AppHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Hsyk extends Model
{
    use HasFactory;

    protected $table;

    protected $primaryKey = 'id';
//    public $incrementing = false;
//    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        $this->table = AppHelper::$HSYK;
        parent::__construct($attributes);
    }

    protected $fillable = [
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'HaisoDt' => 'datetime:Ymd',
    ];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->setTimezone('Asia/Tokyo')->format('Y-m-d H:i:s');
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->setTimezone('Asia/Tokyo')->format('Y-m-d H:i:s');
    }

    public function scopeInsertDownload($query) {
        return $query->where(function ($q){
            $q->where('update_flg', '!=', AppHelper::$isUpdate)
                ->orWhereNull('update_flg');
        });
    }

    public function scopeWhereTantCdOrNull($query, $tantCd) {
        return $query->where(function ($q) use ($tantCd){
            $q->where('HaisoTantCd', '=', $tantCd)
                ->orWhereNull('HaisoTantCd');
        });
    }

    public static $selectColsApiDownload = [
        'KigyoCd', 'BusyoCd', 'EigyoCd',
        'HanbaiCd', 'BukkenNo', 'RirekiNo',
        'HaisoDt', 'Barcode', 'HonKigo', 'HonNo'
    ];

    public static  $deleteColsApiDownload = [
        'KigyoCd', 'HaisoCenterCd', 'HaisoTantCd', 'BusyoCd',
        'EigyoCd', 'HanbaiCd', 'BukkenNo', 'RirekiNo'
    ];
}
