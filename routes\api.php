<?php

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\DownloadController;
use App\Http\Controllers\Api\UploadController;
use App\Http\Controllers\Api\TestController;
use App\Helpers\AppHelper;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Route::post('/register', [AuthController::class, 'register']);
Route::post('/regist', [AuthController::class, 'regist']);
// Route::post('/getdevice', [AuthController::class, 'getdevice']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/login2', [AuthController::class, 'login2']);

Route::middleware(['api','jwt'])->group(function () {
    // Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);

    Route::get('/dload', [DownloadController::class, 'dload']);
    Route::get('/dloadblk', [DownloadController::class, 'dloadblk']);
    Route::get('/master', [DownloadController::class, 'master']);

    Route::post('/uload', [UploadController::class, 'uload']);
    Route::post('/uloadblk', [UploadController::class, 'uloadblk']);
});
// Route::post('/uload', [UploadController::class, 'uload']);

// Route::get('/device', [TestController::class, 'device']);
// Route::get('/tant', [TestController::class, 'tant']);
// Route::get('/bbkn', [TestController::class, 'bbkn']);
// Route::get('/bulk', [TestController::class, 'bulk']);
// Route::get('/code', [TestController::class, 'code']);
// Route::get('/hbkn', [TestController::class, 'hbkn']);
// Route::get('/hiso', [TestController::class, 'hiso']);
// Route::get('/hiyk', [TestController::class, 'hiyk']);
// Route::get('/hsyk', [TestController::class, 'hsyk']);
// Route::get('/srmk', [TestController::class, 'srmk']);
Route::get('/hello', function () {
    return response()->json([
        'current_time' => Carbon::now()->toDateTimeString()
    ], 200);
})->name('hello');
