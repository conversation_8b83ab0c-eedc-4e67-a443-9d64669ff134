[2025-05-29 16:44:20] local.INFO: ====================LOG START[DEADLOCK_FAILURE]====================  
[2025-05-29 16:44:20] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'deadlock_failure' => 
  array (
    'operation' => 'DELETE from t_hiyk',
    'final_attempt' => 0,
    'error' => 'SQLSTATE[40001]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Transaction (Process ID 56) was deadlocked on lock resources with another process and has been chosen as the deadlock victim. Rerun the transaction. (SQL: 
            DELETE FROM t_hiyk
            WHERE id IN
                ( SELECT q.id
                    FROM t_hiyk q
                    INNER JOIN t_hiyk_temp u ON q.KigyoCd = u.KigyoCd AND q.HaisoCenterCd = u.HaisoCenterCd AND q.BusyoCd = u.BusyoCd AND q.EigyoCd = u.EigyoCd AND q.HanbaiCd = u.HanbaiCd AND q.BukkenNo = u.BukkenNo AND q.HaisoTantCd = u.HaisoTantCd
                    GROUP BY q.id
                );
            )',
  ),
)  
[2025-05-29 16:44:20] local.INFO: ====================LOG END====================  
[2025-05-29 16:44:20] local.INFO: ====================LOG START[TRANSACTION_DEADLOCK]====================  
[2025-05-29 16:44:20] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'deadlock_failure' => 
  array (
    'operation' => 'DELETE from t_hiyk',
    'final_attempt' => 0,
    'error' => 'SQLSTATE[40001]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Transaction (Process ID 56) was deadlocked on lock resources with another process and has been chosen as the deadlock victim. Rerun the transaction. (SQL: 
            DELETE FROM t_hiyk
            WHERE id IN
                ( SELECT q.id
                    FROM t_hiyk q
                    INNER JOIN t_hiyk_temp u ON q.KigyoCd = u.KigyoCd AND q.HaisoCenterCd = u.HaisoCenterCd AND q.BusyoCd = u.BusyoCd AND q.EigyoCd = u.EigyoCd AND q.HanbaiCd = u.HanbaiCd AND q.BukkenNo = u.BukkenNo AND q.HaisoTantCd = u.HaisoTantCd
                    GROUP BY q.id
                );
            )',
  ),
  'exception' => 
  array (
    'message' => 'SQLSTATE[40001]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Transaction (Process ID 56) was deadlocked on lock resources with another process and has been chosen as the deadlock victim. Rerun the transaction. (SQL: 
            DELETE FROM t_hiyk
            WHERE id IN
                ( SELECT q.id
                    FROM t_hiyk q
                    INNER JOIN t_hiyk_temp u ON q.KigyoCd = u.KigyoCd AND q.HaisoCenterCd = u.HaisoCenterCd AND q.BusyoCd = u.BusyoCd AND q.EigyoCd = u.EigyoCd AND q.HanbaiCd = u.HanbaiCd AND q.BukkenNo = u.BukkenNo AND q.HaisoTantCd = u.HaisoTantCd
                    GROUP BY q.id
                );
            )',
    'line' => 712,
    'file' => 'D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',
    'trace' => '#0 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback(\'\\r\\n            D...\', Array, Object(Closure))
#1 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(502): Illuminate\\Database\\Connection->run(\'\\r\\n            D...\', Array, Object(Closure))
#2 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(442): Illuminate\\Database\\Connection->statement(\'\\r\\n            D...\')
#3 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(261): Illuminate\\Database\\DatabaseManager->__call(\'statement\', Array)
#4 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(275): Illuminate\\Support\\Facades\\Facade::__callStatic(\'statement\', Array)
#5 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(349): App\\Http\\Controllers\\api\\UploadController->App\\Http\\Controllers\\api\\{closure}()
#6 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(276): App\\Http\\Controllers\\api\\UploadController->executeWithDeadlockRetry(Object(Closure), \'DELETE from t_h...\')
#7 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(177): App\\Http\\Controllers\\api\\UploadController->lastSql(\'t_hiyk\', \'t_hiyk_temp\', Array, Array, \'**********-qosy...\')
#8 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(34): App\\Http\\Controllers\\api\\UploadController->common(Object(Illuminate\\Http\\Request))
#9 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\api\\UploadController->uload(Object(Illuminate\\Http\\Request))
#10 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction(\'uload\', Array)
#11 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\api\\UploadController), \'uload\')
#12 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#14 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(103): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#19 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), \'api\', Object(Closure))
#20 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), \'api\')
#21 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\project\\NttHaisoMobile\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 D:\\work\\project\\NttHaisoMobile\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 {main}',
  ),
  'deadlock_in_transaction' => true,
)  
[2025-05-29 16:44:20] local.INFO: ====================LOG END====================  
[2025-05-29 16:44:20] local.INFO: ====================LOG START[EM2003]====================  
[2025-05-29 16:44:20] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'deadlock_failure' => 
  array (
    'operation' => 'DELETE from t_hiyk',
    'final_attempt' => 0,
    'error' => 'SQLSTATE[40001]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Transaction (Process ID 56) was deadlocked on lock resources with another process and has been chosen as the deadlock victim. Rerun the transaction. (SQL: 
            DELETE FROM t_hiyk
            WHERE id IN
                ( SELECT q.id
                    FROM t_hiyk q
                    INNER JOIN t_hiyk_temp u ON q.KigyoCd = u.KigyoCd AND q.HaisoCenterCd = u.HaisoCenterCd AND q.BusyoCd = u.BusyoCd AND q.EigyoCd = u.EigyoCd AND q.HanbaiCd = u.HanbaiCd AND q.BukkenNo = u.BukkenNo AND q.HaisoTantCd = u.HaisoTantCd
                    GROUP BY q.id
                );
            )',
  ),
  'exception' => 
  array (
    'message' => 'SQLSTATE[40001]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Transaction (Process ID 56) was deadlocked on lock resources with another process and has been chosen as the deadlock victim. Rerun the transaction. (SQL: 
            DELETE FROM t_hiyk
            WHERE id IN
                ( SELECT q.id
                    FROM t_hiyk q
                    INNER JOIN t_hiyk_temp u ON q.KigyoCd = u.KigyoCd AND q.HaisoCenterCd = u.HaisoCenterCd AND q.BusyoCd = u.BusyoCd AND q.EigyoCd = u.EigyoCd AND q.HanbaiCd = u.HanbaiCd AND q.BukkenNo = u.BukkenNo AND q.HaisoTantCd = u.HaisoTantCd
                    GROUP BY q.id
                );
            )',
    'line' => 712,
    'file' => 'D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',
    'trace' => '#0 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(672): Illuminate\\Database\\Connection->runQueryCallback(\'\\r\\n            D...\', Array, Object(Closure))
#1 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(502): Illuminate\\Database\\Connection->run(\'\\r\\n            D...\', Array, Object(Closure))
#2 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\DatabaseManager.php(442): Illuminate\\Database\\Connection->statement(\'\\r\\n            D...\')
#3 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(261): Illuminate\\Database\\DatabaseManager->__call(\'statement\', Array)
#4 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(275): Illuminate\\Support\\Facades\\Facade::__callStatic(\'statement\', Array)
#5 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(349): App\\Http\\Controllers\\api\\UploadController->App\\Http\\Controllers\\api\\{closure}()
#6 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(276): App\\Http\\Controllers\\api\\UploadController->executeWithDeadlockRetry(Object(Closure), \'DELETE from t_h...\')
#7 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(177): App\\Http\\Controllers\\api\\UploadController->lastSql(\'t_hiyk\', \'t_hiyk_temp\', Array, Array, \'**********-qosy...\')
#8 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(34): App\\Http\\Controllers\\api\\UploadController->common(Object(Illuminate\\Http\\Request))
#9 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\api\\UploadController->uload(Object(Illuminate\\Http\\Request))
#10 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction(\'uload\', Array)
#11 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\api\\UploadController), \'uload\')
#12 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#13 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#14 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(103): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#19 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), \'api\', Object(Closure))
#20 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), \'api\')
#21 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#23 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#24 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#25 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#26 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#27 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\work\\project\\NttHaisoMobile\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 D:\\work\\project\\NttHaisoMobile\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 {main}',
  ),
  'deadlock_in_transaction' => true,
)  
[2025-05-29 16:44:20] local.INFO: ====================LOG END====================  
[2025-06-03 14:52:43] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 14:52:43] local.INFO: array (
  'url' => 'http://************/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22405,0,0,1,000063115000,1,20250524,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22405,706,0,0,1,000063115000,1,20250524,2,*********,,,1,10:41:40',
          1 => '22405,706,0,0,1,000063115000,1,20250524,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'Attempt to read property "HaisoCenterCd" on null - 186',
)  
[2025-06-03 14:52:43] local.INFO: ====================LOG END====================  
[2025-06-03 14:54:20] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 14:54:20] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'SQLSTATE[40001]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Transaction (Process ID 72) was deadlocked on lock resources with another process and has been chosen as the deadlock victim. Rerun the transaction. (SQL: 
        INSERT INTO t_hiso_temp_1 (RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at)
        SELECT RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at
        FROM t_hiso_temp
        WHERE RandomString = \'1748930059-A214DS3hGzqLhgUs7RrDWrcASd8BpIDLMUBR5n3ahu8AH9lTZz8DIioU9Afj4MjjsAo6WP84eCPF3SkwD5q6j3BJpx\';
        
        WITH cte AS (
            SELECT 
                KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at,
                ROW_NUMBER() OVER (
                    PARTITION BY 
                        KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
                    ORDER BY 
                        KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
                ) row_num
            FROM 
                t_hiso_temp_1
            WHERE 
                RandomString = \'1748930059-A214DS3hGzqLhgUs7RrDWrcASd8BpIDLMUBR5n3ahu8AH9lTZz8DIioU9Afj4MjjsAo6WP84eCPF3SkwD5q6j3BJpx\'
            )
        DELETE FROM cte
        WHERE row_num > 1;
        
        INSERT INTO t_hiso SELECT t2.KigyoCd,t2.HaisoCenterCd,t2.BusyoCd,t2.EigyoCd,t2.HanbaiCd,t2.BukkenNo,t2.RirekiNo,t2.HaisoDt,t2.IdouKbn,t2.HaisoTantCd,t2.SechiKeitai,t2.Yoki11Ryo,t2.Yoki11Num,t2.Yoki21Ryo,t2.Yoki21Num,t2.Yoki12Ryo,t2.Yoki12Num,t2.Yoki22Ryo,t2.Yoki22Num,t2.Shishininfo,t2.Kuikomi,t2.ShiyoHanten,t2.KataChg,t2.RyoChg,t2.AllChg,t2.GasKireRiyu,t2.Ido,t2.Keido,t2.HaisoTenken1,t2.HaisoTenken2,t2.HaisoTenken3,t2.HaisoTenken4,t2.HaisoTenken5,t2.HaisoTenken6,t2.HaisoTenken7,t2.HaisoTenken8,t2.HaisoTenken9,t2.HaisoTenken10,t2.HaisoTenken11,t2.HaisoTenken12,t2.HaisoTenken13,t2.HaisoTenken14,t2.HaisoTenken15,t2.HaisoTenken16,t2.HaisoTenken17,t2.HaisoTenken18,t2.HaisoTenken19,t2.HaisoTenken20,t2.HaisoTenken21,t2.HaisoTenken22,t2.HaisoTenken23,t2.HaisoTenken24,t2.HaisoTenken25,t2.HaisoTenken26,t2.ShiyoZan,t2.YobiZan,t2.HaisoTime,t2.MeterKekokuCheck,t2.MeterKekokuA,t2.MeterKekokuB,t2.MeterKekokuC,t2.MeterKekokuR,t2.created_at,t2.updated_at FROM t_hiso_temp_1 t2
        LEFT JOIN t_hiso t1 ON t1.KigyoCd = t2.KigyoCd AND t1.HaisoCenterCd = t2.HaisoCenterCd AND t1.BusyoCd = t2.BusyoCd AND t1.EigyoCd = t2.EigyoCd AND t1.HanbaiCd = t2.HanbaiCd AND t1.BukkenNo = t2.BukkenNo AND t1.RirekiNo = t2.RirekiNo AND t1.HaisoDt = t2.HaisoDt
        WHERE t1.KigyoCd IS NULL AND t1.HaisoCenterCd IS NULL AND t1.BusyoCd IS NULL AND t1.EigyoCd IS NULL AND t1.HanbaiCd IS NULL AND t1.BukkenNo IS NULL AND t1.RirekiNo IS NULL AND t1.HaisoDt IS NULL AND t2.RandomString = \'1748930059-A214DS3hGzqLhgUs7RrDWrcASd8BpIDLMUBR5n3ahu8AH9lTZz8DIioU9Afj4MjjsAo6WP84eCPF3SkwD5q6j3BJpx\';
        
        DELETE FROM t_hiso_temp WHERE id NOT IN (
            SELECT id FROM (
                SELECT MAX(id) as id,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt 
                FROM t_hiso_temp
                WHERE RandomString = \'1748930059-A214DS3hGzqLhgUs7RrDWrcASd8BpIDLMUBR5n3ahu8AH9lTZz8DIioU9Afj4MjjsAo6WP84eCPF3SkwD5q6j3BJpx\'
                GROUP BY KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
            ) 
            AS t1
        );
        UPDATE t1 
            SET t1.IdouKbn = t2.IdouKbn,t1.HaisoTantCd = t2.HaisoTantCd,t1.SechiKeitai = t2.SechiKeitai,t1.Yoki11Ryo = t2.Yoki11Ryo,t1.Yoki11Num = t2.Yoki11Num,t1.Yoki21Ryo = t2.Yoki21Ryo,t1.Yoki21Num = t2.Yoki21Num,t1.Yoki12Ryo = t2.Yoki12Ryo,t1.Yoki12Num = t2.Yoki12Num,t1.Yoki22Ryo = t2.Yoki22Ryo,t1.Yoki22Num = t2.Yoki22Num,t1.Shishininfo = t2.Shishininfo,t1.Kuikomi = t2.Kuikomi,t1.ShiyoHanten = t2.ShiyoHanten,t1.KataChg = t2.KataChg,t1.RyoChg = t2.RyoChg,t1.AllChg = t2.AllChg,t1.GasKireRiyu = t2.GasKireRiyu,t1.Ido = t2.Ido,t1.Keido = t2.Keido,t1.HaisoTenken1 = t2.HaisoTenken1,t1.HaisoTenken2 = t2.HaisoTenken2,t1.HaisoTenken3 = t2.HaisoTenken3,t1.HaisoTenken4 = t2.HaisoTenken4,t1.HaisoTenken5 = t2.HaisoTenken5,t1.HaisoTenken6 = t2.HaisoTenken6,t1.HaisoTenken7 = t2.HaisoTenken7,t1.HaisoTenken8 = t2.HaisoTenken8,t1.HaisoTenken9 = t2.HaisoTenken9,t1.HaisoTenken10 = t2.HaisoTenken10,t1.HaisoTenken11 = t2.HaisoTenken11,t1.HaisoTenken12 = t2.HaisoTenken12,t1.HaisoTenken13 = t2.HaisoTenken13,t1.HaisoTenken14 = t2.HaisoTenken14,t1.HaisoTenken15 = t2.HaisoTenken15,t1.HaisoTenken16 = t2.HaisoTenken16,t1.HaisoTenken17 = t2.HaisoTenken17,t1.HaisoTenken18 = t2.HaisoTenken18,t1.HaisoTenken19 = t2.HaisoTenken19,t1.HaisoTenken20 = t2.HaisoTenken20,t1.HaisoTenken21 = t2.HaisoTenken21,t1.HaisoTenken22 = t2.HaisoTenken22,t1.HaisoTenken23 = t2.HaisoTenken23,t1.HaisoTenken24 = t2.HaisoTenken24,t1.HaisoTenken25 = t2.HaisoTenken25,t1.HaisoTenken26 = t2.HaisoTenken26,t1.ShiyoZan = t2.ShiyoZan,t1.YobiZan = t2.YobiZan,t1.HaisoTime = t2.HaisoTime,t1.MeterKekokuCheck = t2.MeterKekokuCheck,t1.MeterKekokuA = t2.MeterKekokuA,t1.MeterKekokuB = t2.MeterKekokuB,t1.MeterKekokuC = t2.MeterKekokuC,t1.MeterKekokuR = t2.MeterKekokuR,t1.updated_at = t2.updated_at 
            FROM t_hiso t1, t_hiso_temp t2 
            WHERE 
                t1.KigyoCd = t2.KigyoCd AND t1.HaisoCenterCd = t2.HaisoCenterCd AND t1.BusyoCd = t2.BusyoCd AND t1.EigyoCd = t2.EigyoCd AND t1.HanbaiCd = t2.HanbaiCd AND t1.BukkenNo = t2.BukkenNo AND t1.RirekiNo = t2.RirekiNo AND t1.HaisoDt = t2.HaisoDt AND t2.RandomString = \'1748930059-A214DS3hGzqLhgUs7RrDWrcASd8BpIDLMUBR5n3ahu8AH9lTZz8DIioU9Afj4MjjsAo6WP84eCPF3SkwD5q6j3BJpx\';
           
            ) - 712',
)  
[2025-06-03 14:54:20] local.INFO: ====================LOG END====================  
[2025-06-03 14:54:25] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 14:54:25] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'SQLSTATE[40001]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Transaction (Process ID 72) was deadlocked on lock resources with another process and has been chosen as the deadlock victim. Rerun the transaction. (SQL: 
        INSERT INTO t_hiso_temp_1 (RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at)
        SELECT RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at
        FROM t_hiso_temp
        WHERE RandomString = \'1748930062-vhZ86hX1MRMtbbecZX2gUsTCZVVGySe8aqaHuZi6qAgPNI5mEFPTQyCTHAkdSoU5ViCcSWiBI0WFoAHffKp2wsuCZX\';
        
        WITH cte AS (
            SELECT 
                KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at,
                ROW_NUMBER() OVER (
                    PARTITION BY 
                        KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
                    ORDER BY 
                        KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
                ) row_num
            FROM 
                t_hiso_temp_1
            WHERE 
                RandomString = \'1748930062-vhZ86hX1MRMtbbecZX2gUsTCZVVGySe8aqaHuZi6qAgPNI5mEFPTQyCTHAkdSoU5ViCcSWiBI0WFoAHffKp2wsuCZX\'
            )
        DELETE FROM cte
        WHERE row_num > 1;
        
        INSERT INTO t_hiso SELECT t2.KigyoCd,t2.HaisoCenterCd,t2.BusyoCd,t2.EigyoCd,t2.HanbaiCd,t2.BukkenNo,t2.RirekiNo,t2.HaisoDt,t2.IdouKbn,t2.HaisoTantCd,t2.SechiKeitai,t2.Yoki11Ryo,t2.Yoki11Num,t2.Yoki21Ryo,t2.Yoki21Num,t2.Yoki12Ryo,t2.Yoki12Num,t2.Yoki22Ryo,t2.Yoki22Num,t2.Shishininfo,t2.Kuikomi,t2.ShiyoHanten,t2.KataChg,t2.RyoChg,t2.AllChg,t2.GasKireRiyu,t2.Ido,t2.Keido,t2.HaisoTenken1,t2.HaisoTenken2,t2.HaisoTenken3,t2.HaisoTenken4,t2.HaisoTenken5,t2.HaisoTenken6,t2.HaisoTenken7,t2.HaisoTenken8,t2.HaisoTenken9,t2.HaisoTenken10,t2.HaisoTenken11,t2.HaisoTenken12,t2.HaisoTenken13,t2.HaisoTenken14,t2.HaisoTenken15,t2.HaisoTenken16,t2.HaisoTenken17,t2.HaisoTenken18,t2.HaisoTenken19,t2.HaisoTenken20,t2.HaisoTenken21,t2.HaisoTenken22,t2.HaisoTenken23,t2.HaisoTenken24,t2.HaisoTenken25,t2.HaisoTenken26,t2.ShiyoZan,t2.YobiZan,t2.HaisoTime,t2.MeterKekokuCheck,t2.MeterKekokuA,t2.MeterKekokuB,t2.MeterKekokuC,t2.MeterKekokuR,t2.created_at,t2.updated_at FROM t_hiso_temp_1 t2
        LEFT JOIN t_hiso t1 ON t1.KigyoCd = t2.KigyoCd AND t1.HaisoCenterCd = t2.HaisoCenterCd AND t1.BusyoCd = t2.BusyoCd AND t1.EigyoCd = t2.EigyoCd AND t1.HanbaiCd = t2.HanbaiCd AND t1.BukkenNo = t2.BukkenNo AND t1.RirekiNo = t2.RirekiNo AND t1.HaisoDt = t2.HaisoDt
        WHERE t1.KigyoCd IS NULL AND t1.HaisoCenterCd IS NULL AND t1.BusyoCd IS NULL AND t1.EigyoCd IS NULL AND t1.HanbaiCd IS NULL AND t1.BukkenNo IS NULL AND t1.RirekiNo IS NULL AND t1.HaisoDt IS NULL AND t2.RandomString = \'1748930062-vhZ86hX1MRMtbbecZX2gUsTCZVVGySe8aqaHuZi6qAgPNI5mEFPTQyCTHAkdSoU5ViCcSWiBI0WFoAHffKp2wsuCZX\';
        
        DELETE FROM t_hiso_temp WHERE id NOT IN (
            SELECT id FROM (
                SELECT MAX(id) as id,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt 
                FROM t_hiso_temp
                WHERE RandomString = \'1748930062-vhZ86hX1MRMtbbecZX2gUsTCZVVGySe8aqaHuZi6qAgPNI5mEFPTQyCTHAkdSoU5ViCcSWiBI0WFoAHffKp2wsuCZX\'
                GROUP BY KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
            ) 
            AS t1
        );
        UPDATE t1 
            SET t1.IdouKbn = t2.IdouKbn,t1.HaisoTantCd = t2.HaisoTantCd,t1.SechiKeitai = t2.SechiKeitai,t1.Yoki11Ryo = t2.Yoki11Ryo,t1.Yoki11Num = t2.Yoki11Num,t1.Yoki21Ryo = t2.Yoki21Ryo,t1.Yoki21Num = t2.Yoki21Num,t1.Yoki12Ryo = t2.Yoki12Ryo,t1.Yoki12Num = t2.Yoki12Num,t1.Yoki22Ryo = t2.Yoki22Ryo,t1.Yoki22Num = t2.Yoki22Num,t1.Shishininfo = t2.Shishininfo,t1.Kuikomi = t2.Kuikomi,t1.ShiyoHanten = t2.ShiyoHanten,t1.KataChg = t2.KataChg,t1.RyoChg = t2.RyoChg,t1.AllChg = t2.AllChg,t1.GasKireRiyu = t2.GasKireRiyu,t1.Ido = t2.Ido,t1.Keido = t2.Keido,t1.HaisoTenken1 = t2.HaisoTenken1,t1.HaisoTenken2 = t2.HaisoTenken2,t1.HaisoTenken3 = t2.HaisoTenken3,t1.HaisoTenken4 = t2.HaisoTenken4,t1.HaisoTenken5 = t2.HaisoTenken5,t1.HaisoTenken6 = t2.HaisoTenken6,t1.HaisoTenken7 = t2.HaisoTenken7,t1.HaisoTenken8 = t2.HaisoTenken8,t1.HaisoTenken9 = t2.HaisoTenken9,t1.HaisoTenken10 = t2.HaisoTenken10,t1.HaisoTenken11 = t2.HaisoTenken11,t1.HaisoTenken12 = t2.HaisoTenken12,t1.HaisoTenken13 = t2.HaisoTenken13,t1.HaisoTenken14 = t2.HaisoTenken14,t1.HaisoTenken15 = t2.HaisoTenken15,t1.HaisoTenken16 = t2.HaisoTenken16,t1.HaisoTenken17 = t2.HaisoTenken17,t1.HaisoTenken18 = t2.HaisoTenken18,t1.HaisoTenken19 = t2.HaisoTenken19,t1.HaisoTenken20 = t2.HaisoTenken20,t1.HaisoTenken21 = t2.HaisoTenken21,t1.HaisoTenken22 = t2.HaisoTenken22,t1.HaisoTenken23 = t2.HaisoTenken23,t1.HaisoTenken24 = t2.HaisoTenken24,t1.HaisoTenken25 = t2.HaisoTenken25,t1.HaisoTenken26 = t2.HaisoTenken26,t1.ShiyoZan = t2.ShiyoZan,t1.YobiZan = t2.YobiZan,t1.HaisoTime = t2.HaisoTime,t1.MeterKekokuCheck = t2.MeterKekokuCheck,t1.MeterKekokuA = t2.MeterKekokuA,t1.MeterKekokuB = t2.MeterKekokuB,t1.MeterKekokuC = t2.MeterKekokuC,t1.MeterKekokuR = t2.MeterKekokuR,t1.updated_at = t2.updated_at 
            FROM t_hiso t1, t_hiso_temp t2 
            WHERE 
                t1.KigyoCd = t2.KigyoCd AND t1.HaisoCenterCd = t2.HaisoCenterCd AND t1.BusyoCd = t2.BusyoCd AND t1.EigyoCd = t2.EigyoCd AND t1.HanbaiCd = t2.HanbaiCd AND t1.BukkenNo = t2.BukkenNo AND t1.RirekiNo = t2.RirekiNo AND t1.HaisoDt = t2.HaisoDt AND t2.RandomString = \'1748930062-vhZ86hX1MRMtbbecZX2gUsTCZVVGySe8aqaHuZi6qAgPNI5mEFPTQyCTHAkdSoU5ViCcSWiBI0WFoAHffKp2wsuCZX\';
           
            ) - 712',
)  
[2025-06-03 14:54:25] local.INFO: ====================LOG END====================  
[2025-06-03 14:55:52] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 14:55:52] local.INFO: array (
  'url' => 'http://************/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22405,0,0,1,000063115000,1,20250524,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22405,706,0,0,1,000063115000,1,20250524,2,*********,,,1,10:41:40',
          1 => '22405,706,0,0,1,000063115000,1,20250524,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'SQLSTATE[40001]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Transaction (Process ID 82) was deadlocked on lock resources with another process and has been chosen as the deadlock victim. Rerun the transaction. (SQL: 
        INSERT INTO t_hiso_temp_1 (RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at)
        SELECT RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at
        FROM t_hiso_temp
        WHERE RandomString = \'1748930149-LdTdjuNCT9KCMZDhcXELxbkIaMvnVY93SH0qqIqYFMq0cvJ07UlKR6Me5ioT8tOKrYuOihaS9xIsz4YEySgYGNUGD5\';
        
        WITH cte AS (
            SELECT 
                KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,Shishininfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at,
                ROW_NUMBER() OVER (
                    PARTITION BY 
                        KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
                    ORDER BY 
                        KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
                ) row_num
            FROM 
                t_hiso_temp_1
            WHERE 
                RandomString = \'1748930149-LdTdjuNCT9KCMZDhcXELxbkIaMvnVY93SH0qqIqYFMq0cvJ07UlKR6Me5ioT8tOKrYuOihaS9xIsz4YEySgYGNUGD5\'
            )
        DELETE FROM cte
        WHERE row_num > 1;
        
        INSERT INTO t_hiso SELECT t2.KigyoCd,t2.HaisoCenterCd,t2.BusyoCd,t2.EigyoCd,t2.HanbaiCd,t2.BukkenNo,t2.RirekiNo,t2.HaisoDt,t2.IdouKbn,t2.HaisoTantCd,t2.SechiKeitai,t2.Yoki11Ryo,t2.Yoki11Num,t2.Yoki21Ryo,t2.Yoki21Num,t2.Yoki12Ryo,t2.Yoki12Num,t2.Yoki22Ryo,t2.Yoki22Num,t2.Shishininfo,t2.Kuikomi,t2.ShiyoHanten,t2.KataChg,t2.RyoChg,t2.AllChg,t2.GasKireRiyu,t2.Ido,t2.Keido,t2.HaisoTenken1,t2.HaisoTenken2,t2.HaisoTenken3,t2.HaisoTenken4,t2.HaisoTenken5,t2.HaisoTenken6,t2.HaisoTenken7,t2.HaisoTenken8,t2.HaisoTenken9,t2.HaisoTenken10,t2.HaisoTenken11,t2.HaisoTenken12,t2.HaisoTenken13,t2.HaisoTenken14,t2.HaisoTenken15,t2.HaisoTenken16,t2.HaisoTenken17,t2.HaisoTenken18,t2.HaisoTenken19,t2.HaisoTenken20,t2.HaisoTenken21,t2.HaisoTenken22,t2.HaisoTenken23,t2.HaisoTenken24,t2.HaisoTenken25,t2.HaisoTenken26,t2.ShiyoZan,t2.YobiZan,t2.HaisoTime,t2.MeterKekokuCheck,t2.MeterKekokuA,t2.MeterKekokuB,t2.MeterKekokuC,t2.MeterKekokuR,t2.created_at,t2.updated_at FROM t_hiso_temp_1 t2
        LEFT JOIN t_hiso t1 ON t1.KigyoCd = t2.KigyoCd AND t1.HaisoCenterCd = t2.HaisoCenterCd AND t1.BusyoCd = t2.BusyoCd AND t1.EigyoCd = t2.EigyoCd AND t1.HanbaiCd = t2.HanbaiCd AND t1.BukkenNo = t2.BukkenNo AND t1.RirekiNo = t2.RirekiNo AND t1.HaisoDt = t2.HaisoDt
        WHERE t1.KigyoCd IS NULL AND t1.HaisoCenterCd IS NULL AND t1.BusyoCd IS NULL AND t1.EigyoCd IS NULL AND t1.HanbaiCd IS NULL AND t1.BukkenNo IS NULL AND t1.RirekiNo IS NULL AND t1.HaisoDt IS NULL AND t2.RandomString = \'1748930149-LdTdjuNCT9KCMZDhcXELxbkIaMvnVY93SH0qqIqYFMq0cvJ07UlKR6Me5ioT8tOKrYuOihaS9xIsz4YEySgYGNUGD5\';
        
        DELETE FROM t_hiso_temp WHERE id NOT IN (
            SELECT id FROM (
                SELECT MAX(id) as id,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt 
                FROM t_hiso_temp
                WHERE RandomString = \'1748930149-LdTdjuNCT9KCMZDhcXELxbkIaMvnVY93SH0qqIqYFMq0cvJ07UlKR6Me5ioT8tOKrYuOihaS9xIsz4YEySgYGNUGD5\'
                GROUP BY KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt
            ) 
            AS t1
        );
        UPDATE t1 
            SET t1.IdouKbn = t2.IdouKbn,t1.HaisoTantCd = t2.HaisoTantCd,t1.SechiKeitai = t2.SechiKeitai,t1.Yoki11Ryo = t2.Yoki11Ryo,t1.Yoki11Num = t2.Yoki11Num,t1.Yoki21Ryo = t2.Yoki21Ryo,t1.Yoki21Num = t2.Yoki21Num,t1.Yoki12Ryo = t2.Yoki12Ryo,t1.Yoki12Num = t2.Yoki12Num,t1.Yoki22Ryo = t2.Yoki22Ryo,t1.Yoki22Num = t2.Yoki22Num,t1.Shishininfo = t2.Shishininfo,t1.Kuikomi = t2.Kuikomi,t1.ShiyoHanten = t2.ShiyoHanten,t1.KataChg = t2.KataChg,t1.RyoChg = t2.RyoChg,t1.AllChg = t2.AllChg,t1.GasKireRiyu = t2.GasKireRiyu,t1.Ido = t2.Ido,t1.Keido = t2.Keido,t1.HaisoTenken1 = t2.HaisoTenken1,t1.HaisoTenken2 = t2.HaisoTenken2,t1.HaisoTenken3 = t2.HaisoTenken3,t1.HaisoTenken4 = t2.HaisoTenken4,t1.HaisoTenken5 = t2.HaisoTenken5,t1.HaisoTenken6 = t2.HaisoTenken6,t1.HaisoTenken7 = t2.HaisoTenken7,t1.HaisoTenken8 = t2.HaisoTenken8,t1.HaisoTenken9 = t2.HaisoTenken9,t1.HaisoTenken10 = t2.HaisoTenken10,t1.HaisoTenken11 = t2.HaisoTenken11,t1.HaisoTenken12 = t2.HaisoTenken12,t1.HaisoTenken13 = t2.HaisoTenken13,t1.HaisoTenken14 = t2.HaisoTenken14,t1.HaisoTenken15 = t2.HaisoTenken15,t1.HaisoTenken16 = t2.HaisoTenken16,t1.HaisoTenken17 = t2.HaisoTenken17,t1.HaisoTenken18 = t2.HaisoTenken18,t1.HaisoTenken19 = t2.HaisoTenken19,t1.HaisoTenken20 = t2.HaisoTenken20,t1.HaisoTenken21 = t2.HaisoTenken21,t1.HaisoTenken22 = t2.HaisoTenken22,t1.HaisoTenken23 = t2.HaisoTenken23,t1.HaisoTenken24 = t2.HaisoTenken24,t1.HaisoTenken25 = t2.HaisoTenken25,t1.HaisoTenken26 = t2.HaisoTenken26,t1.ShiyoZan = t2.ShiyoZan,t1.YobiZan = t2.YobiZan,t1.HaisoTime = t2.HaisoTime,t1.MeterKekokuCheck = t2.MeterKekokuCheck,t1.MeterKekokuA = t2.MeterKekokuA,t1.MeterKekokuB = t2.MeterKekokuB,t1.MeterKekokuC = t2.MeterKekokuC,t1.MeterKekokuR = t2.MeterKekokuR,t1.updated_at = t2.updated_at 
            FROM t_hiso t1, t_hiso_temp t2 
            WHERE 
                t1.KigyoCd = t2.KigyoCd AND t1.HaisoCenterCd = t2.HaisoCenterCd AND t1.BusyoCd = t2.BusyoCd AND t1.EigyoCd = t2.EigyoCd AND t1.HanbaiCd = t2.HanbaiCd AND t1.BukkenNo = t2.BukkenNo AND t1.RirekiNo = t2.RirekiNo AND t1.HaisoDt = t2.HaisoDt AND t2.RandomString = \'1748930149-LdTdjuNCT9KCMZDhcXELxbkIaMvnVY93SH0qqIqYFMq0cvJ07UlKR6Me5ioT8tOKrYuOihaS9xIsz4YEySgYGNUGD5\';
           
            ) - 712',
)  
[2025-06-03 14:55:52] local.INFO: ====================LOG END====================  
[2025-06-03 15:28:15] local.ERROR: Target class [App\Http\Controllers\Api\UploadController] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [App\\Http\\Controllers\\Api\\UploadController] does not exist. at D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:879)
[stacktrace]
#0 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#1 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#2 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#3 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#4 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#5 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#6 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#7 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#8 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#9 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#10 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#11 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#12 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#13 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\work\\project\\NttHaisoMobile\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#31 D:\\work\\project\\NttHaisoMobile\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#32 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"App\\Http\\Controllers\\Api\\UploadController\" does not exist at D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:877)
[stacktrace]
#0 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#1 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#2 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#3 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#4 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#5 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#6 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#7 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#8 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#9 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#10 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#11 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#12 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#13 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#14 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\work\\project\\NttHaisoMobile\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#31 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#32 D:\\work\\project\\NttHaisoMobile\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#33 {main}
"} 
[2025-06-03 15:28:36] local.INFO: ====================LOG START[TRANSACTION_ERROR]====================  
[2025-06-03 15:28:36] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 
  array (
    'message' => 'SQLSTATE[42S02]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name \'#t_hiso_temp\'. (SQL: INSERT INTO #t_hiso_temp (RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,ShishinInfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at) VALUES (\'1748932116-PonNPq8vtl5AlsVHzWu5YIftSLH0YDXNON1Ant1I09Xta5eX9tBhLLUtCltkNVLDegFJXtDXJR771dR90OsPKyIA8x\',\'22408\',\'10\',\'0\',\'0\',\'1\',\'000063115000\',\'1\',\'2025-05-29 00:00:00\',\'2\',\'706\',\'3\',\'20\',\'1\',\'20\',\'1\',\'0\',\'0\',\'0\',\'0\',\'73.7\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0.0\',\'0.0\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0.0\',\'0.0\',\'10:41:40\',\'0\',\'0\',\'0\',\'0\',\'0\',\'2025-06-03 15:28:36\',\'2025-06-03 15:28:36\');)',
    'line' => 712,
    'file' => 'D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',
  ),
)  
[2025-06-03 15:28:36] local.INFO: ====================LOG END====================  
[2025-06-03 15:28:36] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 15:28:36] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,2,706,3,20,1,20,1,0,0,0,0,73.7,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 
  array (
    'message' => 'SQLSTATE[42S02]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid object name \'#t_hiso_temp\'. (SQL: INSERT INTO #t_hiso_temp (RandomString,KigyoCd,HaisoCenterCd,BusyoCd,EigyoCd,HanbaiCd,BukkenNo,RirekiNo,HaisoDt,IdouKbn,HaisoTantCd,SechiKeitai,Yoki11Ryo,Yoki11Num,Yoki21Ryo,Yoki21Num,Yoki12Ryo,Yoki12Num,Yoki22Ryo,Yoki22Num,ShishinInfo,Kuikomi,ShiyoHanten,KataChg,RyoChg,AllChg,GasKireRiyu,Ido,Keido,HaisoTenken1,HaisoTenken2,HaisoTenken3,HaisoTenken4,HaisoTenken5,HaisoTenken6,HaisoTenken7,HaisoTenken8,HaisoTenken9,HaisoTenken10,HaisoTenken11,HaisoTenken12,HaisoTenken13,HaisoTenken14,HaisoTenken15,HaisoTenken16,HaisoTenken17,HaisoTenken18,HaisoTenken19,HaisoTenken20,HaisoTenken21,HaisoTenken22,HaisoTenken23,HaisoTenken24,HaisoTenken25,HaisoTenken26,ShiyoZan,YobiZan,HaisoTime,MeterKekokuCheck,MeterKekokuA,MeterKekokuB,MeterKekokuC,MeterKekokuR,created_at,updated_at) VALUES (\'1748932116-PonNPq8vtl5AlsVHzWu5YIftSLH0YDXNON1Ant1I09Xta5eX9tBhLLUtCltkNVLDegFJXtDXJR771dR90OsPKyIA8x\',\'22408\',\'10\',\'0\',\'0\',\'1\',\'000063115000\',\'1\',\'2025-05-29 00:00:00\',\'2\',\'706\',\'3\',\'20\',\'1\',\'20\',\'1\',\'0\',\'0\',\'0\',\'0\',\'73.7\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0.0\',\'0.0\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'1\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0\',\'0.0\',\'0.0\',\'10:41:40\',\'0\',\'0\',\'0\',\'0\',\'0\',\'2025-06-03 15:28:36\',\'2025-06-03 15:28:36\');)',
    'line' => 712,
    'file' => 'D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php',
  ),
)  
[2025-06-03 15:28:36] local.INFO: ====================LOG END====================  
[2025-06-03 15:50:40] local.ERROR: PDO::exec(): Argument #1 ($statement) cannot be empty {"exception":"[object] (ValueError(code: 0): PDO::exec(): Argument #1 ($statement) cannot be empty at D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php:306)
[stacktrace]
#0 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(306): PDO->exec('')
#1 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(177): App\\Http\\Controllers\\api\\UploadController->lastSql('t_hiso', '#t_hiso_temp', Array, Array, '**********-dlU2...', Object(PDO))
#2 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(34): App\\Http\\Controllers\\api\\UploadController->common(Object(Illuminate\\Http\\Request))
#3 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\api\\UploadController->uload(Object(Illuminate\\Http\\Request))
#4 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('uload', Array)
#5 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\api\\UploadController), 'uload')
#6 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#7 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#8 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(103): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#13 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#14 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#15 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#18 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#19 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#20 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#21 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\work\\project\\NttHaisoMobile\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 D:\\work\\project\\NttHaisoMobile\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-06-03 15:59:57] local.ERROR: PDO::exec(): Argument #1 ($statement) cannot be empty {"exception":"[object] (ValueError(code: 0): PDO::exec(): Argument #1 ($statement) cannot be empty at D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php:280)
[stacktrace]
#0 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(280): PDO->exec('')
#1 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(177): App\\Http\\Controllers\\api\\UploadController->lastSql('t_hiso', '#t_hiso_temp', Array, Array, '**********-x7pu...', Object(PDO))
#2 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(34): App\\Http\\Controllers\\api\\UploadController->common(Object(Illuminate\\Http\\Request))
#3 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\api\\UploadController->uload(Object(Illuminate\\Http\\Request))
#4 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('uload', Array)
#5 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\api\\UploadController), 'uload')
#6 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#7 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#8 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(103): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#13 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#14 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#15 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#17 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#18 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#19 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#20 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#21 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\work\\project\\NttHaisoMobile\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#39 D:\\work\\project\\NttHaisoMobile\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#40 {main}
"} 
[2025-06-03 16:01:23] local.INFO: ====================LOG START[EM2001]====================  
[2025-06-03 16:01:23] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
  ),
  'message' => 
  array (
    0 => 'upload は、必ず指定してください。',
  ),
)  
[2025-06-03 16:01:23] local.INFO: ====================LOG END====================  
[2025-06-03 18:26:52] local.INFO: ====================LOG START[EM2002]====================  
[2025-06-03 18:26:52] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,2,706,3,20,1,20,1,0,0,0,0,73.88,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'errors' => 
  array (
    '30_HISO' => 
    array (
      0 => 'row 1 : The shishin info format is invalid.',
    ),
    '31_HIYK' => 
    array (
    ),
  ),
)  
[2025-06-03 18:26:52] local.INFO: ====================LOG END====================  
[2025-06-03 18:48:12] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 18:48:12] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,3,706,3,20,1,20,1,0,0,0,0,73.9,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'SQLSTATE[42S22]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name \'RandomString\'. - 169',
)  
[2025-06-03 18:48:12] local.INFO: ====================LOG END====================  
[2025-06-03 18:48:30] local.ERROR: Undefined variable $th {"exception":"[object] (ErrorException(code: 0): Undefined variable $th at D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php:183)
[stacktrace]
#0 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(183): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'D:\\\\work\\\\project...', 183)
#1 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(34): App\\Http\\Controllers\\api\\UploadController->common(Object(Illuminate\\Http\\Request))
#2 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\api\\UploadController->uload(Object(Illuminate\\Http\\Request))
#3 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('uload', Array)
#4 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\api\\UploadController), 'uload')
#5 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#7 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(103): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#12 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#13 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#14 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\project\\NttHaisoMobile\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 D:\\work\\project\\NttHaisoMobile\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-06-03 18:48:54] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 18:48:54] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,3,706,3,20,1,20,1,0,0,0,0,73.9,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'SQLSTATE[42S22]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name \'RandomString\'. - 169',
)  
[2025-06-03 18:48:54] local.INFO: ====================LOG END====================  
[2025-06-03 18:49:34] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 18:49:34] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,3,706,3,20,1,20,1,0,0,0,0,73.9,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'SQLSTATE[42S22]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name \'RandomString\'. - 169',
)  
[2025-06-03 18:49:34] local.INFO: ====================LOG END====================  
[2025-06-03 18:50:19] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 18:50:19] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,3,706,3,20,1,20,1,0,0,0,0,73.9,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'SQLSTATE[42S22]: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Invalid column name \'RandomString\'. - 169',
)  
[2025-06-03 18:50:19] local.INFO: ====================LOG END====================  
[2025-06-03 18:51:10] local.ERROR: syntax error, unexpected token "else" {"exception":"[object] (ParseError(code: 0): syntax error, unexpected token \"else\" at D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php:41)
[stacktrace]
#0 D:\\work\\project\\NttHaisoMobile\\vendor\\composer\\ClassLoader.php(428): Composer\\Autoload\\includeFile('D:\\\\work\\\\project...')
#1 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Contro...')
#2 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(877): ReflectionClass->__construct('App\\\\Http\\\\Contro...')
#3 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(758): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#4 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(851): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#5 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(694): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#6 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(836): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#7 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(276): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#8 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1080): Illuminate\\Routing\\Route->getController()
#9 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(1023): Illuminate\\Routing\\Route->controllerMiddleware()
#10 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(734): Illuminate\\Routing\\Route->gatherMiddleware()
#11 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(714): Illuminate\\Routing\\Router->gatherRouteMiddleware(Object(Illuminate\\Routing\\Route))
#12 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#13 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#14 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#15 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#16 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\work\\project\\NttHaisoMobile\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#34 D:\\work\\project\\NttHaisoMobile\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#35 {main}
"} 
[2025-06-03 18:51:50] local.ERROR: Undefined variable $th {"exception":"[object] (ErrorException(code: 0): Undefined variable $th at D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php:183)
[stacktrace]
#0 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(183): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined varia...', 'D:\\\\work\\\\project...', 183)
#1 D:\\work\\project\\NttHaisoMobile\\app\\Http\\Controllers\\Api\\UploadController.php(34): App\\Http\\Controllers\\api\\UploadController->common(Object(Illuminate\\Http\\Request))
#2 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\api\\UploadController->uload(Object(Illuminate\\Http\\Request))
#3 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('uload', Array)
#4 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(262): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\api\\UploadController), 'uload')
#5 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#6 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#7 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(103): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#12 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#13 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#14 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(723): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#16 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#17 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#18 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#19 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#20 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\work\\project\\NttHaisoMobile\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(38): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 D:\\work\\project\\NttHaisoMobile\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#38 D:\\work\\project\\NttHaisoMobile\\public\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#39 {main}
"} 
[2025-06-03 19:06:34] local.INFO: ====================LOG START[EM1003]====================  
[2025-06-03 19:06:34] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/login',
  'request' => 
  array (
    'KigyoCd' => '88888',
    'TantCd' => '1',
    'password' => 'Aa123456!',
  ),
  'message' => 
  array (
    0 => 'u u i d は、必ず指定してください。',
    1 => 'handy pw は、必ず指定してください。',
  ),
)  
[2025-06-03 19:06:34] local.INFO: ====================LOG END====================  
[2025-06-03 19:10:40] local.INFO: ====================LOG START[EM1003]====================  
[2025-06-03 19:10:40] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/login',
  'request' => 
  array (
    'KigyoCd' => '88888',
    'TantCd' => '1',
    'HandyPw' => 'Hide password to avoid user data leakage',
  ),
  'message' => 
  array (
    0 => 'u u i d は、必ず指定してください。',
  ),
)  
[2025-06-03 19:10:40] local.INFO: ====================LOG END====================  
[2025-06-03 19:11:36] local.INFO: ====================LOG START[EM1003]====================  
[2025-06-03 19:11:36] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/login',
  'request' => 
  array (
    'KigyoCd' => '88888',
    'TantCd' => '1',
    'HandyPw' => 'Hide password to avoid user data leakage',
  ),
  'message' => 
  array (
    0 => 'u u i d は、必ず指定してください。',
  ),
)  
[2025-06-03 19:11:36] local.INFO: ====================LOG END====================  
[2025-06-03 19:21:52] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 19:21:52] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,3,706,3,20,1,20,1,0,0,0,0,73.9,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'Attempt to read property "HaisoCenterCd" on null - 192',
)  
[2025-06-03 19:21:52] local.INFO: ====================LOG END====================  
[2025-06-03 19:22:03] local.INFO: ====================LOG START[EM2003]====================  
[2025-06-03 19:22:03] local.INFO: array (
  'url' => 'http://ntthaisomobile.test/mobile/api/uload',
  'request' => 
  array (
    'upload' => 
    array (
      0 => 
      array (
        'fileType' => '30_HISO',
        'datas' => 
        array (
          0 => '22408,0,0,1,000063115000,1,20250529,3,706,3,20,1,20,1,0,0,0,0,73.9,0,0,0,0,0,0,0.0,0.0,11111111111000000000000000,0.0,0.0,10:41:40,0,0,0,0,0',
        ),
      ),
      1 => 
      array (
        'fileType' => '31_HIYK',
        'datas' => 
        array (
          0 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,1,10:41:40',
          1 => '22408,706,0,0,1,000063115000,1,20250529,2,*********,,,0,10:41:40',
        ),
      ),
    ),
  ),
  'exception' => 'Attempt to read property "HaisoCenterCd" on null - 192',
)  
[2025-06-03 19:22:03] local.INFO: ====================LOG END====================  
