<?php
/**
 * <PERSON><PERSON>t test deadlock cho API upload
 * <PERSON><PERSON><PERSON>hiề<PERSON> request đồng thời để test deadlock prevention
 */

class DeadlockTester
{
    private $baseUrl;
    private $loginCredentials;
    private $token;
    private $logFile;
    
    public function __construct($config = [])
    {
        // C<PERSON>u hình mặc định
        $this->baseUrl = $config['base_url'] ?? 'http://localhost:8000';
        $this->loginCredentials = $config['login'] ?? [
            'KigyoCd' => 22408,
            'TantCd' => 1,
            'HandyPw' => 'password123'
        ];
        $this->logFile = $config['log_file'] ?? 'deadlock_test_' . date('Y-m-d_H-i-s') . '.log';
        
        $this->log("=== Deadlock Test Started ===");
        $this->log("Base URL: " . $this->baseUrl);
        $this->log("Login Credentials: " . json_encode($this->loginCredentials, J<PERSON><PERSON>_UNESCAPED_UNICODE));
    }
    
    /**
     * Đăng nhập và lấy token
     */
    public function login()
    {
        $url = $this->baseUrl . '/mobile/api/login2';
        
        $this->log("Attempting login to: " . $url);
        
        $response = $this->makeRequest($url, 'POST', $this->loginCredentials);
        
        if (!$response) {
            throw new Exception("Login failed: No response");
        }
        
        $data = json_decode($response, true);
        
        if (!$data || $data['code'] !== 200) {
            $this->log("Login failed: " . $response);
            throw new Exception("Login failed: " . ($data['message'] ?? 'Unknown error'));
        }
        
        $this->token = $data['token'];
        $this->log("Login successful. Token: " . substr($this->token, 0, 50) . "...");
        
        return $this->token;
    }
    
    /**
     * Tạo dữ liệu test cho upload
     */
    public function generateTestData($fileType = '30_HISO', $recordCount = 10)
    {
        $datas = [];
        
        for ($i = 0; $i < $recordCount; $i++) {
            switch ($fileType) {
                case '30_HISO':
                    $datas[] = $this->generateHisoData($i);
                    break;
                case '31_HIYK':
                    $datas[] = $this->generateHiykData($i);
                    break;
                case '33_BULK':
                    $datas[] = $this->generateBulkData($i);
                    break;
            }
        }
        
        return [
            'upload' => [
                [
                    'fileType' => $fileType,
                    'datas' => $datas
                ]
            ]
        ];
    }
    
    /**
     * Tạo dữ liệu HISO test
     */
    private function generateHisoData($index)
    {
        $baseData = [
            22408, // KigyoCd
            0,     // BusyoCd
            0,     // EigyoCd
            1,     // HanbaiCd
            sprintf('%012d', 63115000 + $index), // BukkenNo
            1,     // RirekiNo
            '20250529', // HaisoDt
            2,     // IdouKbn
            706,   // HaisoTantCd
            3,     // SechiKeitai
            20, 1, 20, 1, 0, 0, 0, 0, // Yoki fields
            73.7,  // ShishinInfo
            0, 0, 0, 0, 0, 0, // Flags
            0.0, 0.0, // Ido, Keido
            // HaisoTenken 1-26
            1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
            0.0, 0.0, // ShiyoZan, YobiZan
            '10:41:40', // HaisoTime
            0, 0, 0, 0, 0 // MeterKekoku fields
        ];
        
        return implode(',', $baseData);
    }
    
    /**
     * Tạo dữ liệu HIYK test
     */
    private function generateHiykData($index)
    {
        $baseData = [
            22408, // KigyoCd
            706,   // HaisoTantCd
            0,     // BusyoCd
            0,     // EigyoCd
            1,     // HanbaiCd
            sprintf('%012d', 63115000 + $index), // BukkenNo
            1,     // RirekiNo
            '20250529', // HaisoDt
            2,     // IdouKbn
            'BC123456789', // Barcode
            'HK001', // HonKigo
            'HN12345', // HonNo
            1,     // HikitoriKbn
            '10:41:40' // SoshinDt (time only)
        ];
        
        return implode(',', $baseData);
    }
    
    /**
     * Tạo dữ liệu BULK test
     */
    private function generateBulkData($index)
    {
        $baseData = [
            22408, // KigyoCd
            0,     // BusyoCd
            0,     // EigyoCd
            1,     // HanbaiCd
            sprintf('%012d', 63115000 + $index), // BukkenNo
            1,     // RirekiNo
            '20250529', // HaisoDt
            2,     // IdouKbn
            706,   // HaisoTantCd
            3,     // SechiKeitai
            20, 1, 20, 1, 0, 0, 0, 0, // Yoki fields
            73.7,  // ShishinInfo
            0.0, 0.0, // Ido, Keido
            // HaisoTenken 1-26
            1,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,
            '10:41:40', // HaisoTime
            0, 0, 0, 0, 0, // MeterKekoku fields
            1,     // SyaryoCd
            10.5, 20.3, 150.7 // JyutenMa, JyutenGo, JyutenRyo
        ];
        
        return implode(',', $baseData);
    }
    
    /**
     * Thực hiện upload request
     */
    public function upload($data, $endpoint = 'uload')
    {
        if (!$this->token) {
            throw new Exception("No token available. Please login first.");
        }
        
        $url = $this->baseUrl . '/mobile/api/' . $endpoint;
        
        $headers = [
            'Authorization: Bearer ' . $this->token,
            'Content-Type: application/json'
        ];
        
        $response = $this->makeRequest($url, 'POST', $data, $headers);
        
        return json_decode($response, true);
    }
    
    /**
     * Test deadlock với nhiều request đồng thời
     */
    public function testConcurrentUploads($concurrency = 5, $fileType = '30_HISO', $recordCount = 10)
    {
        $this->log("=== Starting Concurrent Upload Test ===");
        $this->log("Concurrency: $concurrency");
        $this->log("File Type: $fileType");
        $this->log("Records per request: $recordCount");
        
        // Đăng nhập trước
        $this->login();
        
        // Tạo dữ liệu test
        $testData = $this->generateTestData($fileType, $recordCount);
        
        // Tạo các process con để chạy đồng thời
        $processes = [];
        $startTime = microtime(true);
        
        for ($i = 0; $i < $concurrency; $i++) {
            $processId = $i + 1;
            $this->log("Starting process $processId");
            
            // Tạo dữ liệu riêng cho mỗi process (để tránh conflict)
            $processData = $this->generateTestData($fileType, $recordCount);
            
            // Modify BukkenNo để tránh duplicate
            foreach ($processData['upload'][0]['datas'] as &$data) {
                $parts = explode(',', $data);
                $bukkenNo = intval($parts[4]) + ($processId * 1000); // Offset by process ID
                $parts[4] = sprintf('%012d', $bukkenNo);
                $data = implode(',', $parts);
            }
            
            $pid = pcntl_fork();
            
            if ($pid == -1) {
                throw new Exception("Could not fork process $processId");
            } elseif ($pid == 0) {
                // Child process
                $this->runUploadProcess($processId, $processData);
                exit(0);
            } else {
                // Parent process
                $processes[$processId] = $pid;
            }
        }
        
        // Đợi tất cả processes hoàn thành
        $results = [];
        foreach ($processes as $processId => $pid) {
            $status = 0;
            pcntl_waitpid($pid, $status);
            $results[$processId] = $status;
            $this->log("Process $processId completed with status: $status");
        }
        
        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;
        
        $this->log("=== Concurrent Upload Test Completed ===");
        $this->log("Total time: " . number_format($totalTime, 2) . " seconds");
        $this->log("Results: " . json_encode($results));
        
        return $results;
    }
    
    /**
     * Chạy upload process (được gọi bởi child process)
     */
    private function runUploadProcess($processId, $data)
    {
        $this->log("Process $processId: Starting upload");
        $startTime = microtime(true);
        
        try {
            $response = $this->upload($data);
            $endTime = microtime(true);
            $duration = $endTime - $startTime;
            
            $this->log("Process $processId: Upload completed in " . number_format($duration, 2) . "s");
            $this->log("Process $processId: Response: " . json_encode($response, JSON_UNESCAPED_UNICODE));
            
            if ($response && $response['code'] === 200) {
                $this->log("Process $processId: SUCCESS");
            } else {
                $this->log("Process $processId: FAILED - " . ($response['message'] ?? 'Unknown error'));
            }
            
        } catch (Exception $e) {
            $this->log("Process $processId: EXCEPTION - " . $e->getMessage());
        }
    }
    
    /**
     * Thực hiện HTTP request
     */
    private function makeRequest($url, $method = 'GET', $data = null, $headers = [])
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => array_merge([
                'Accept: application/json',
                'User-Agent: DeadlockTester/1.0'
            ], $headers),
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            throw new Exception("CURL Error: $error");
        }
        
        if ($httpCode >= 400) {
            $this->log("HTTP Error $httpCode: $response");
        }
        
        return $response;
    }
    
    /**
     * Ghi log
     */
    private function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] $message" . PHP_EOL;
        
        echo $logMessage;
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }
}
