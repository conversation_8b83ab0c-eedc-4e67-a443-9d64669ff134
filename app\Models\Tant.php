<?php

namespace App\Models;

use App\Helpers\AppHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
//use Laravel\Sanctum\HasApiTokens;
use Carbon\Carbon;
use Tymon\JWTAuth\Contracts\JWTSubject;

class Tant extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable;
//    use HasApiTokens, HasFactory, Notifiable;

    protected $table;

//    protected $primaryKey = 'id';
//    public $incrementing = false;
//    public $timestamps = false;

//    protected $connection = 'mysql';

    public function __construct(array $attributes = [])
    {
        $this->table = AppHelper::$TANT;
        parent::__construct($attributes);
    }

    public function getAuthPassword()
    {
        return $this->HandyPw;
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'KigyoCd',
        'TantCd',
        'Name',
        'KenshinTantFlg',
        'HaisoTantFlg',
        'KyotenCd',
        'HaisoCenterCd',
        'HandyPw',
        'HandyKengen',
        'ErrCnt',
        'DisableFlg',
        'last_login',
        'factor_qr_code',
        'factor_cert_flag',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'HandyPw',
        'ErrCnt',
        'DisableFlg',
        'last_login',
        'created_at',
        'updated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'KenshinTantFlg' => 'int',
        'HaisoTantFlg' => 'int',
    ];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->setTimezone('Asia/Tokyo')->format('Y-m-d H:i:s');
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->setTimezone('Asia/Tokyo')->format('Y-m-d H:i:s');
    }

    public function getLastLoginAttribute($value)
    {
        return Carbon::parse($value)->setTimezone('Asia/Tokyo')->format('Y-m-d H:i:s');
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier() {
//        return 'TantCd';
        return $this->getKey();
    }
    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims() {
        return [];
    }
}
