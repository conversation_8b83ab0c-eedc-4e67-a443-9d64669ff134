<?php

namespace App\Helpers;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AppHelper
{
    public static $DEVICE = 'm_device';
    public static $TANT = 'm_tant';
    public static $SRMK = 'm_srmk';
    public static $BBKN = 't_bbkn';
    public static $BULK = 't_bulk';
    public static $HBKN = 't_hbkn';
    public static $HISO = 't_hiso';
    public static $HIYK = 't_hiso';
    public static $HSYK = 't_hsyk';
    public static $CODE = 'm_code';
    public static $COMPANY = 'm_company';

    public static $ACTIVE = 1;
    public static $NOT_ACTIVE = 0;
    public static $DEVICE_DISABLE = 2;

    public static $DISABLE = 1;
    public static $NOT_DISABLE = 0;

    public static $MAX_ERRCNT = 5;
    public static $LOCK_TIME = 900;
    public static $SLEEP_TIME = 21600;//6h

    public static $LIMIT_INS = 2000;
    public static $DEFAULT_400_MESS = '送信JSONが不備でした。';

    public static $isUpdate = 1;

    public static $numErrorWhenGenToken = 10;
    public static $deviceNameWhenGenToken = 'genUUID-login';

    public static function getLoginUUID()
    {
        return auth('api')->payload()->get('UUID');
    }

    public static function resError($status,$message)
    {
        return response([
            'code' => $status,
            'message' => $message
        ], $status);
    }

    public static $uploadApi = [
        'uload' => ['30_HISO', '31_HIYK'],
        'uloadblk' => ['33_BULK'],
    ];

    public static $uploadFileType = [
        '30_HISO' => [
            'primary' => [
                'KigyoCd', 'HaisoCenterCd', 'BusyoCd', 'EigyoCd', 'HanbaiCd',
                'BukkenNo', 'RirekiNo', 'HaisoDt',
            ],
            'update' => [
                'IdouKbn', 'HaisoTantCd', 'SechiKeitai', 'Yoki11Ryo',
                'Yoki11Num', 'Yoki21Ryo', 'Yoki21Num', 'Yoki12Ryo', 'Yoki12Num',
                'Yoki22Ryo', 'Yoki22Num', 'Shishininfo', 'Kuikomi',
                'ShiyoHanten', 'KataChg', 'RyoChg', 'AllChg', 'GasKireRiyu',
                'Ido', 'Keido', 'HaisoTenken1', 'HaisoTenken2', 'HaisoTenken3',
                'HaisoTenken4', 'HaisoTenken5', 'HaisoTenken6', 'HaisoTenken7',
                'HaisoTenken8', 'HaisoTenken9', 'HaisoTenken10', 'HaisoTenken11',
                'HaisoTenken12', 'HaisoTenken13', 'HaisoTenken14', 'HaisoTenken15',
                'HaisoTenken16', 'HaisoTenken17', 'HaisoTenken18', 'HaisoTenken19',
                'HaisoTenken20', 'HaisoTenken21', 'HaisoTenken22', 'HaisoTenken23',
                'HaisoTenken24', 'HaisoTenken25', 'HaisoTenken26', 'ShiyoZan',
                'YobiZan', 'HaisoTime', 'MeterKekokuCheck', 'MeterKekokuA',
                'MeterKekokuB', 'MeterKekokuC', 'MeterKekokuR',
            ],
        ],
        '31_HIYK' => [
            'primary' => [
                'KigyoCd', 'HaisoCenterCd', 'BusyoCd', 'EigyoCd', 'HanbaiCd',
                'BukkenNo', 'RirekiNo', 'HaisoDt', 'IdouKbn', 'Barcode',
            ],
            'update' => [
                'HaisoTantCd', 'HonKigo', 'HonNo', 'HikitoriKbn', 'SoshinDt',
            ],
        ],
        '33_BULK' => [
            'primary' => [
                'KigyoCd', 'HaisoCenterCd', 'BusyoCd', 'EigyoCd', 'HanbaiCd',
                'BukkenNo', 'HaisoDt',
            ],
            'update' => [
                'IdouKbn', 'RirekiNo', 'HaisoTantCd', 'SechiKeitai', 'Yoki11Ryo',
                'Yoki11Num', 'Yoki21Ryo', 'Yoki21Num', 'Yoki12Ryo', 'Yoki12Num',
                'Yoki22Ryo', 'Yoki22Num', 'ShishinInfo', 'Ido', 'Keido',
                'HaisoTenken1', 'HaisoTenken2',  'HaisoTenken3', 'HaisoTenken4',
                'HaisoTenken5', 'HaisoTenken6', 'HaisoTenken7', 'HaisoTenken8',
                'HaisoTenken9', 'HaisoTenken10', 'HaisoTenken11', 'HaisoTenken12',
                'HaisoTenken13', 'HaisoTenken14', 'HaisoTenken15', 'HaisoTenken16',
                'HaisoTenken17', 'HaisoTenken18', 'HaisoTenken19', 'HaisoTenken20',
                'HaisoTenken21', 'HaisoTenken22', 'HaisoTenken23', 'HaisoTenken24',
                'HaisoTenken25', 'HaisoTenken26', 'HaisoTime', 'MeterKekokuCheck',
                'MeterKekokuA', 'MeterKekokuB', 'MeterKekokuC', 'MeterKekokuR',
                'SyaryoCd', 'JyutenMa', 'JyutenGo', 'JyutenRyo',
            ],
        ],
    ];

    public static $insertTb = [
        't_hiyk' => [
            'KigyoCd', 'HaisoCenterCd', 'HaisoTantCd', 'BusyoCd', 'EigyoCd',
            'HanbaiCd', 'BukkenNo', 'RirekiNo', 'HaisoDt', 'IdouKbn', 'Barcode',
            'HonKigo', 'HonNo', 'HikitoriKbn', 'SoshinDt',
        ],
        't_bulk' => [
            'KigyoCd', 'HaisoCenterCd', 'BusyoCd', 'EigyoCd', 'HanbaiCd', 'BukkenNo',
            'RirekiNo', 'HaisoDt', 'IdouKbn', 'HaisoTantCd', 'SechiKeitai',
            'Yoki11Ryo', 'Yoki11Num', 'Yoki21Ryo', 'Yoki21Num', 'Yoki12Ryo',
            'Yoki12Num', 'Yoki22Ryo', 'Yoki22Num', 'ShishinInfo', 'Ido', 'Keido',
            'HaisoTenken1', 'HaisoTenken2',  'HaisoTenken3', 'HaisoTenken4',
            'HaisoTenken5', 'HaisoTenken6', 'HaisoTenken7', 'HaisoTenken8',
            'HaisoTenken9', 'HaisoTenken10', 'HaisoTenken11', 'HaisoTenken12',
            'HaisoTenken13', 'HaisoTenken14', 'HaisoTenken15', 'HaisoTenken16',
            'HaisoTenken17', 'HaisoTenken18', 'HaisoTenken19', 'HaisoTenken20',
            'HaisoTenken21', 'HaisoTenken22', 'HaisoTenken23', 'HaisoTenken24',
            'HaisoTenken25', 'HaisoTenken26', 'HaisoTime', 'MeterKekokuCheck',
            'MeterKekokuA', 'MeterKekokuB', 'MeterKekokuC', 'MeterKekokuR',
            'SyaryoCd', 'JyutenMa', 'JyutenGo', 'JyutenRyo',
        ],
    ];

    public static $dateField = [
        '30_HISO' => ['HaisoDt',],
        '31_HIYK' => ['HaisoDt','SoshinDt',],
        '33_BULK' => ['HaisoDt',],
    ];

    public static $rules = [
        '30_HISO' => [
            'RandomString' => 'bail|required|string|max:200',//temp
            'KigyoCd' => 'bail|required|integer',
            'HaisoCenterCd' => 'bail|required|integer|between:0,9999',
            'BusyoCd' => 'bail|required|integer',
            'EigyoCd' => 'bail|required|integer',
            'HanbaiCd' => 'bail|required|integer|between:-9999,9999',
            'BukkenNo' => 'bail|required|string|max:12',
            'RirekiNo' => 'bail|required|integer|between:0,999',
            'HaisoDt' => 'bail|required|date',
            'IdouKbn' => 'bail|required|integer|between:0,9999',
            'HaisoTantCd' => 'bail|nullable|integer|between:0,99999999',
            'SechiKeitai' => 'bail|nullable|integer|between:0,9999',
            'Yoki11Ryo' => 'bail|nullable|integer|between:0,99999',
            'Yoki11Num' => 'bail|nullable|integer|between:0,999',
            'Yoki21Ryo' => 'bail|nullable|integer|between:0,99999',
            'Yoki21Num' => 'bail|nullable|integer|between:0,999',
            'Yoki12Ryo' => 'bail|nullable|integer|between:0,99999',
            'Yoki12Num' => 'bail|nullable|integer|between:0,999',
            'Yoki22Ryo' => 'bail|nullable|integer|between:0,99999',
            'Yoki22Num' => 'bail|nullable|integer|between:0,999',
            'ShishinInfo' => ['bail', 'nullable', 'numeric', 'between:0,999999.9', 'regex:/^(0|[1-9]\d{0,5}+)(\.\d{1}+)?$/'], //decimal(7,1)
            'Kuikomi' => 'bail|nullable|integer|between:0,9999',
            'ShiyoHanten' => 'bail|nullable|integer|between:0,9999',
            'KataChg' => 'bail|nullable|integer|between:0,9999',
            'RyoChg' => 'bail|nullable|integer|between:0,9999',
            'AllChg' => 'bail|nullable|integer|between:0,9999',
            'GasKireRiyu' => 'bail|nullable|integer|between:0,9999',
            'Ido' => ['bail', 'nullable', 'numeric', 'between:0,999.999999', 'regex:/^(0|[1-9]\d{0,2}+)(\.\d{1,6}+)?$/'], //decimal(9,6)
            'Keido' => ['bail', 'nullable', 'numeric', 'between:0,999.999999', 'regex:/^(0|[1-9]\d{0,2}+)(\.\d{1,6}+)?$/'], //decimal(9,6)
            'HaisoTenken1' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken2' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken3' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken4' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken5' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken6' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken7' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken8' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken9' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken10' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken11' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken12' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken13' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken14' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken15' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken16' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken17' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken18' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken19' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken20' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken21' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken22' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken23' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken24' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken25' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken26' => 'bail|nullable|integer|between:0,9',
            'ShiyoZan' => ['bail', 'nullable', 'numeric', 'between:0,999999.9', 'regex:/^(0|[1-9]\d{0,5}+)(\.\d{1}+)?$/'], //decimal(7,1)
            'YobiZan' => ['bail', 'nullable', 'numeric', 'between:0,999999.9', 'regex:/^(0|[1-9]\d{0,5}+)(\.\d{1}+)?$/'], //decimal(7,1)
            'HaisoTime' => 'bail|nullable|string|max:8',
            'MeterKekokuCheck' => 'bail|nullable|integer|between:0,9',
            'MeterKekokuA' => 'bail|nullable|integer|between:0,9',
            'MeterKekokuB' => 'bail|nullable|integer|between:0,9',
            'MeterKekokuC' => 'bail|nullable|integer|between:0,9',
            'MeterKekokuR' => 'bail|nullable|integer|between:0,9',
        ],
        '31_HIYK' => [
            'RandomString' => 'bail|required|string|max:200',//temp
            'KigyoCd' => 'bail|required|integer',
            'HaisoCenterCd' => 'bail|required|integer|between:0,9999',
            'HaisoTantCd' => 'bail|nullable|integer|between:0,99999999',
            'BusyoCd' => 'bail|required|integer',
            'EigyoCd' => 'bail|required|integer',
            'HanbaiCd' => 'bail|required|integer|between:-9999,9999',
            'BukkenNo' => 'bail|required|string|max:12',
            'RirekiNo' => 'bail|required|integer|between:0,999',
            'HaisoDt' => 'bail|nullable|date',
            'IdouKbn' => 'bail|required|integer|between:0,9999',
            'Barcode' => 'bail|nullable|string|max:30',
            'HonKigo' => 'bail|nullable|string|max:5',
            'HonNo' => 'bail|nullable|string|max:7',
            'HikitoriKbn' => 'bail|nullable|integer|between:0,9999',
            'SoshinDt' => 'bail|nullable|date_format:Y-m-d H:i:s',
        ],
        '33_BULK' => [
            'RandomString' => 'bail|required|string|max:200',//temp
            'KigyoCd' => 'bail|required|integer',
            'HaisoCenterCd' => 'bail|required|integer|between:0,9999',
            'BusyoCd' => 'bail|required|integer',
            'EigyoCd' => 'bail|required|integer',
            'HanbaiCd' => 'bail|required|integer|between:-9999,9999',
            'BukkenNo' => 'bail|required|string|max:12',
            'RirekiNo' => 'bail|required|integer|between:0,999',
            'HaisoDt' => 'bail|required|date',
            'IdouKbn' => 'bail|required|integer|between:0,9999',
            'HaisoTantCd' => 'bail|nullable|integer|between:0,99999999',
            'SechiKeitai' => 'bail|nullable|integer|between:0,9999',
            'Yoki11Ryo' => 'bail|nullable|integer|between:0,99999',
            'Yoki11Num' => 'bail|nullable|integer|between:0,999',
            'Yoki21Ryo' => 'bail|nullable|integer|between:0,99999',
            'Yoki21Num' => 'bail|nullable|integer|between:0,999',
            'Yoki12Ryo' => 'bail|nullable|integer|between:0,99999',
            'Yoki12Num' => 'bail|nullable|integer|between:0,999',
            'Yoki22Ryo' => 'bail|nullable|integer|between:0,99999',
            'Yoki22Num' => 'bail|nullable|integer|between:0,999',
            'ShishinInfo' => ['bail', 'nullable', 'numeric', 'between:0,999999.9', 'regex:/^(0|[1-9]\d{0,5}+)(\.\d{1}+)?$/'], //decimal(7,1)
            'Ido' => ['bail', 'nullable', 'numeric', 'between:0,999.999999', 'regex:/^(0|[1-9]\d{0,2}+)(\.\d{1,6}+)?$/'], //decimal(9,6)
            'Keido' => ['bail', 'nullable', 'numeric', 'between:0,999.999999', 'regex:/^(0|[1-9]\d{0,2}+)(\.\d{1,6}+)?$/'], //decimal(9,6)
            'HaisoTenken1' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken2' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken3' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken4' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken5' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken6' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken7' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken8' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken9' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken10' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken11' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken12' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken13' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken14' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken15' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken16' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken17' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken18' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken19' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken20' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken21' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken22' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken23' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken24' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken25' => 'bail|nullable|integer|between:0,9',
            'HaisoTenken26' => 'bail|nullable|integer|between:0,9',
            'HaisoTime' => 'bail|nullable|string|max:8',
            'MeterKekokuCheck' => 'bail|nullable|integer|between:0,9',
            'MeterKekokuA' => 'bail|nullable|integer|between:0,9',
            'MeterKekokuB' => 'bail|nullable|integer|between:0,9',
            'MeterKekokuC' => 'bail|nullable|integer|between:0,9',
            'MeterKekokuR' => 'bail|nullable|integer|between:0,9',
            'SyaryoCd' => 'bail|required|integer|between:0,9999',
            'JyutenMa' => ['bail', 'nullable', 'numeric', 'between:0,999.9', 'regex:/^(0|[1-9]\d{0,2}+)(\.\d{1}+)?$/'], //decimal(4,1)
            'JyutenGo' => ['bail', 'nullable', 'numeric', 'between:0,999.9', 'regex:/^(0|[1-9]\d{0,2}+)(\.\d{1}+)?$/'], //decimal(4,1)
            'JyutenRyo' => ['bail', 'nullable', 'numeric', 'between:0,9999999.9', 'regex:/^(0|[1-9]\d{0,6}+)(\.\d{1}+)?$/'], //decimal(8,1)
        ],
    ];

    public static $primaryKeyDownloadApi = [
        'HSYK' => ['id'],
        'HBKN' => ['KigyoCd','HaisoCenterCd','HaisoTantCd','BusyoCd','EigyoCd','HanbaiCd','BukkenNo','RirekiNo'],
        'master' => ['00_TANT','01_CODE'],
    ];

    public static function replaceStringSql($data, $option = [])
    {
        if (in_array('trim', $option)) {
            $data = trim($data);
        }
        $data = "'" . str_replace("'", "''", $data) . "'";
        return $data;
    }

    public static function checkDateTime($data)
    {
        $date = str_replace("/", "-", substr($data, 0, 19));
        if (strlen($date) < 10) {
            $arr = explode('-', $date);
            if (count($arr) != 3) return null;
            $arr[1] = strlen($arr[1]) == 2 ? $arr[1] : '0' . $arr[1];
            $arr[2] = strlen($arr[2]) == 2 ? $arr[2] : '0' . $arr[2];
            $date = implode('-', $arr);
        }
        if (strlen($date) == 10) {
            $date .= ' 00:00:00';
        }
        if ($date == date('Y-m-d H:i:s', strtotime($date))) return $date;
        return null;
    }

    public static function dateDownload($date)
    {
        return date('Ymd',strtotime($date));
    }

    public static function writeLog($logs, $code = '') {
        try {
            \Log::info("====================LOG START" . ($code ? "[" . $code . "]" : "") . "====================");
            \Log::info($logs);
            \Log::info("====================LOG END====================");
        } catch (\Exception $e) {}
    }

    public static function genRandomStr($len){
        return Str::random($len);
    }

    public static $startToken = 'gen_';

    public static function insertToken($KigyoCd){
        $randomStr = self::$startToken.self::genRandomStr(250);
        try{
            $isSuccess = DB::table(self::$DEVICE)->insert([
                ['UUID' => $randomStr, 'KigyoCd' => $KigyoCd, 'DeviceName' => self::$deviceNameWhenGenToken, 'ActiveFlg' => self::$ACTIVE, 'last_login' => date('Y-m-d H:i:s')]
            ]);
            if(!$isSuccess){
                return false;
            }
        }
        catch (\Exception $e){
            return false;
        }
        return $randomStr;
    }

    public static function actionLogout()
    {
        $uuid = self::getLoginUUID();
        $user = auth('api')->user();
        $pos = strpos($uuid,self::$startToken);
        if($user && $pos !== false && $pos === 0){
            $query = DB::table(AppHelper::$DEVICE)
                ->where('UUID',$uuid)
                ->where('KigyoCd',$user->KigyoCd)
                ->where('DeviceName',self::$deviceNameWhenGenToken)
                ->delete();
        }
        auth('api')->logout();
    }
}

