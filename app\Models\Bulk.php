<?php

namespace App\Models;

use App\Helpers\AppHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Bulk extends Model
{
    use HasFactory;

    protected $table;

//    protected $primaryKey = 'UUID';
    public $incrementing = false;
//    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        $this->table = AppHelper::$BULK;
        parent::__construct($attributes);
    }

    protected $fillable = [
//        'UUID',
//        'KigyoCd',
//        'BusyoCd',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
    ];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->setTimezone('Asia/Tokyo')->format('Y-m-d H:i:s');
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->setTimezone('Asia/Tokyo')->format('Y-m-d H:i:s');
    }
}
