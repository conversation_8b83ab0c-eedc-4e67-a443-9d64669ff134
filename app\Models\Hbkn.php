<?php

namespace App\Models;

use App\Helpers\AppHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Hbkn extends Model
{
    use HasFactory;

    protected $table;

    protected $primaryKey = [
        'KigyoCd', 'HaisoCenterCd', 'HaisoTantCd', 'BusyoCd', 'EigyoCd', 'HanbaiCd', 'BukkenNo', 'RirekiNo'
    ];
    public $incrementing = false;
//    public $timestamps = false;

    public function __construct(array $attributes = [])
    {
        $this->table = AppHelper::$HBKN;
        parent::__construct($attributes);
    }

    protected $fillable = [
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'HaisoSyokiDt' => 'datetime:Ymd',
        'NewKenshinDt' => 'datetime:Ymd',
        'ZenHaisoDt' => 'datetime:Ymd',
        'GasKireYoteiDt' => 'datetime:Ymd',
        'HaisoDt' => 'datetime:Ymd',
    ];

    public function getCreatedAtAttribute($value)
    {
        return Carbon::parse($value)->setTimezone('Asia/Tokyo')->format('Y-m-d H:i:s');
    }

    public function getUpdatedAtAttribute($value)
    {
        return Carbon::parse($value)->setTimezone('Asia/Tokyo')->format('Y-m-d H:i:s');
    }

    public function scopeInsertDownload($query) {
        return $query->where(function ($q){
            $q->where('update_flg', '!=', AppHelper::$isUpdate)
                ->orWhereNull('update_flg');
        });
    }

    public function scopeWhereTantCdOrNull($query, $tantCd) {
        return $query->where(function ($q) use ($tantCd){
            $q->where('HaisoTantCd', '=', $tantCd)
                ->orWhereNull('HaisoTantCd');
        });
    }

    public static $selectColsApiDownload = [
        'KigyoCd', 'BusyoCd', 'EigyoCd', 'HanbaiCd', 'BukkenNo', 'RirekiNo'
        , 'HaisoSyokiDt', 'KokyakuNo', 'KeisyoCd', 'NewKenshinDt', 'NewKenshinShishin'
        , 'NewKensinRyo', 'ZenHaisoDt', 'ZenHaisoShishin', 'ZenHaisoRyo'
        , 'HaisoYoteiShishin', 'GasKireYoteiDt', 'GasKireYoteiShishin'
        , 'SechiKeitai', 'Yoki11Ryo', 'Yoki11Num', 'Yoki21Ryo', 'Yoki21Num'
        , 'Yoki12Ryo', 'Yoki12Num', 'Yoki22Ryo', 'Yoki22Num', 'MeterNo'
        , 'IriguchiIchiCd', 'Ido', 'Keido', 'Name', 'Kana', 'Address1'
        , 'Address2', 'TatemonoMei', 'HeyaNo', 'Shudanshosai', 'HaisoMemo', 'Tel'
    ];

    public static  $deleteColsApiDownload = [
        'KigyoCd', 'HaisoCenterCd', 'HaisoTantCd', 'BusyoCd',
        'EigyoCd', 'HanbaiCd', 'BukkenNo', 'RirekiNo'
    ];
}
