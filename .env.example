APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:VE3rpNZwGSM/M1dn9hHxrqVYVdxUIpZCe7qMcALwVPI=
APP_DEBUG=false
APP_URL=http://ntt-mobile.gas-meter.jp

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlsrv
DB_HOST=*************
DB_PORT=1433
DB_DATABASE=HAISO_COMMON
DB_USERNAME=haisouser
DB_PASSWORD=leontech@sqluser$

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
SLEEP_TIME=21600

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=NAwdv5A8SPaXKxx7qt3n5REZk5VcPZq5KYubuB7Dn0fpfNBXPjigIN0fIxDd0GcN
JWT_TTL=720
